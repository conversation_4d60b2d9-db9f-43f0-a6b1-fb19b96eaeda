import { UserModel } from '../../models/user';
import { BulkAggregatedSurveysData, SurveyModel, SurveyType } from '../../models/survey';
import { InitiativePlain } from '../../models/initiative';
import { SurveyTemplate, SurveyTemplateMinData, SurveyTemplateModel } from '../../models/surveyTemplate';
import { SurveyImporter } from '../survey/SurveyImporter';
import { wwgLogger } from '../wwgLogger';
import { Logger } from 'winston';
import { HttpErrorMessages } from '../../error/ErrorMessages';
import { DefaultBlueprintCode } from '../../survey/blueprints';
import { ObjectId } from 'bson';
import { SurveyUpdateData, TemplateFormData } from './types';
import { DataPeriods, UtrvType } from '../utr/constants';
import { AggregateSurveyCreate, getSurveyAggregator, SurveyAggregator } from '../survey/SurveyAggregator';
import UserError from '../../error/UserError';
import { getNotificationManager, NotificationManager } from '../notification/NotificationManager';
import { AggregationMode } from '../../models/public/universalTrackerType';
import { SurveyTemplateHistoryModel } from '../../models/surveyTemplateHistory';
import { InitiativeRepository } from '../../repository/InitiativeRepository';
import { DEFAULT_AGGREGATED_NAME, getNameFromMergeTags } from './utils';
import { TemplateManager, getTemplateManager } from './TemplateManager';
import { getSurveyDateRange } from '../../util/survey';

export class AggregatedTemplateManager {
  constructor(
    private logger: Logger,
    private notificationManager: NotificationManager,
    private surveyAggregator: SurveyAggregator,
    private templateManager: TemplateManager
  ) {}

  public async createAggregatedTemplate(initiative: InitiativePlain, data: TemplateFormData) {
    const reportingLevels =
      data.reportingLevels && Array.isArray(data.reportingLevels)
        ? data.reportingLevels.map((initiativeId) => new ObjectId(initiativeId))
        : [];

    const createData: SurveyTemplateMinData = {
      type: SurveyType.Aggregation,
      name: data.name,
      unitConfig: { ...SurveyImporter.createUnitConfig(initiative), ...data.unitConfig },
      initiativeId: initiative._id,
      evidenceRequired: false,
      verificationRequired: false,
      sourceName: DefaultBlueprintCode,
      surveyName: data.surveyName ?? DEFAULT_AGGREGATED_NAME,
      hasCurrentInitiatives: data.hasCurrentInitiatives,
      reportingLevels: reportingLevels,
    };

    return SurveyTemplate.create(createData);
  }

  public async updateAggregatedTemplate(template: SurveyTemplateModel, user: UserModel, data: SurveyUpdateData) {
    if (!template || typeof data !== 'object') {
      throw new Error(HttpErrorMessages.BadRequest);
    }

    const whitelistData: Partial<SurveyUpdateData> = {
      name: data.name ?? template.name,
      surveyName: data.surveyName ?? template.name,
      hasCurrentInitiatives: data.hasCurrentInitiatives ?? template.hasCurrentInitiatives,
      reportingLevels: data.reportingLevels ?? template.reportingLevels,
    };

    template.set(whitelistData);

    if (template.isModified()) {
      this.logger.info(`User ${user._id} updated template ${template._id}`);
    }

    return template.save();
  }

  public async getSurveysToAggregate(
    data: Pick<BulkAggregatedSurveysData, 'hasCurrentInitiatives' | 'effectiveDate' | 'period'>,
    reportingCompany: Pick<InitiativePlain, '_id'>
  ) {
    const childrenIds = (await InitiativeRepository.getMainTreeChildren(reportingCompany._id)).map((i) => i._id);

    const { effectiveDate, period = DataPeriods.Yearly } = data;

    const { startDate, endDate } = getSurveyDateRange({ effectiveDate, period });

    const surveys = await this.surveyAggregator.getInitiativeSurveysForAggregation({
      initiativeIds: data.hasCurrentInitiatives
        ? childrenIds
        : childrenIds.filter((id) => !id.equals(reportingCompany._id)),
      startDate,
      endDate,
      period,
    });

    const surveysByInitiativeId = new Map<string, AggregateSurveyCreate[]>();
    surveys.forEach((survey) => {
      const effectiveDate = survey.effectiveDate;
      if (effectiveDate > endDate || effectiveDate < startDate) {
        return; // Mostly for tests, as this should be filtered by query
      }
      const initiativeId = survey.initiativeId.toString();
      const current = surveysByInitiativeId.get(initiativeId) ?? [];
      surveysByInitiativeId.set(initiativeId, [...current, survey]);
    });

    const filteredSurveys: AggregateSurveyCreate[] = [];
    surveysByInitiativeId.forEach((surveys) => {
      const monthlySurveys = surveys.filter((survey) => survey.period === DataPeriods.Monthly);
      const quarterlySurveys = surveys.filter((survey) => survey.period === DataPeriods.Quarterly);
      const yearlySurveys = surveys.filter((survey) => survey.period === DataPeriods.Yearly);
      if (period === DataPeriods.Yearly) {
        if (yearlySurveys.length > 0) {
          filteredSurveys.push(...yearlySurveys);
          return;
        }
        if (quarterlySurveys.length > 0) {
          filteredSurveys.push(...quarterlySurveys);
          return;
        }
      }
      if (period === DataPeriods.Quarterly) {
        if (quarterlySurveys.length > 0) {
          filteredSurveys.push(...quarterlySurveys);
          return;
        }
      }
      filteredSurveys.push(...monthlySurveys);
    });
    return filteredSurveys;
  }

  private async createAggregatedSurveyFromTemplate(
    user: UserModel,
    reportingCompany: InitiativePlain,
    data: BulkAggregatedSurveysData & { surveysToAggregate: AggregateSurveyCreate[] }
  ): Promise<SurveyModel> {
    const {
      utrvType = UtrvType.Actual,
      sourceName = DefaultBlueprintCode,
      effectiveDate,
      period,
      surveysToAggregate,
    } = data;

    return this.surveyAggregator.createAggregatedSurvey({
      name: getNameFromMergeTags(data, reportingCompany),
      type: SurveyType.Aggregation,
      surveysToAggregate,
      sourceName,
      effectiveDate,
      utrvType,
      company: reportingCompany,
      user,
      period,
      aggregationMode: AggregationMode.Children,
    });
  }

  public async createAggregatedSurveys(
    data: BulkAggregatedSurveysData,
    user: UserModel,
    history: SurveyTemplateHistoryModel
  ) {
    const createFunc = async (reportingCompany: InitiativePlain) => {
      const surveysToAggregate = await this.getSurveysToAggregate(data, reportingCompany);
      if (!Array.isArray(surveysToAggregate) || surveysToAggregate.length === 0) {
        throw new UserError(`There was a problem creating a survey for ${reportingCompany.name}`);
      }
      return this.createAggregatedSurveyFromTemplate(user, reportingCompany, { ...data, surveysToAggregate });
    };

    await this.templateManager.batchCreateSurveys(createFunc, history);

    await this.notificationManager
      .sendBulkSurveysGeneratedNotification(user._id, data, history)
      .catch((e) => this.logger.error(e));

    return history;
  }
}

let instance: AggregatedTemplateManager;
export const getAggregatedTemplateManager = () => {
  if (!instance) {
    instance = new AggregatedTemplateManager(
      wwgLogger,
      getNotificationManager(),
      getSurveyAggregator(),
      getTemplateManager()
    );
  }

  return instance;
};
