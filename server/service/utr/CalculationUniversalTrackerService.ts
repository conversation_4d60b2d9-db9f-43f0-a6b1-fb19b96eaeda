/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { resolveVariableByUtrValueType } from "../../rules/utrVariables";
import { ColumnType, UtrValueType, ValueAggregation } from "../../models/public/universalTrackerType";
import UniversalTracker, { UniversalTrackerPlain, Calculation, UtrType, UniversalTrackerModel } from "../../models/universalTracker";
import UniversalTrackerValue, { UniversalTrackerValuePlain } from "../../models/universalTrackerValue";
import { AggregatedUniversalTracker } from "./aggregation/AggregatedUniversalTracker";
import { calculateFormula, isNAError, isNRError } from "../../rules/calculation/formula";
import { ActionList, UtrvType } from "./constants";
import { RowData, SourceTypes } from "../../models/public/universalTrackerValueType";
import Initiative from "../../models/initiative";
import { ObjectId } from 'bson';
import { ConfigurationVariableSetup } from "../../survey/compositeUtrConfigs";
import { wwgLogger } from "../wwgLogger";
import UserError from "../../error/UserError";
import CustomReport from "../../models/customReport";
import { AggregationMode } from "../../models/public/universalTrackerType";

type Utrv = UniversalTrackerValuePlain | AggregatedUniversalTracker | undefined;

interface MetricSubmitDataUtr {
  utr: UniversalTrackerPlain;
  isTable: boolean;
  isValueList: boolean;
  valueListCode: string | undefined;
  valueListMatch: string | undefined;
  columnType: ColumnType | undefined;
}

interface CommonMetricSubmitData {
  name: string;
  primaryUtrCode: string;
  primaryValueListCode?: string;
  primaryColumnCode?: string;
}

interface TextMetricSubmitData extends CommonMetricSubmitData {
  showAs: 'text';
}

interface TotalMetricSubmitData extends CommonMetricSubmitData {
  showAs: 'total';
}

interface PercentageMetricSubmitData extends CommonMetricSubmitData {
  showAs: 'percentage';
  percentageOf: 'count' | 'metric';
  secondaryUtrCode: string;
  secondaryValueListCode?: string;
  secondaryColumnCode?: string;
}

export type MetricSubmitData = TotalMetricSubmitData | PercentageMetricSubmitData | TextMetricSubmitData;

export const ensureMap = (vs: Map<string, ConfigurationVariableSetup> | { [key: string]: ConfigurationVariableSetup }): Map<string, ConfigurationVariableSetup> => {
  if (vs instanceof Map) {
    return vs;
  }
  const map = new Map();
  Object.keys(vs).forEach((letter) => {
    map.set(letter, vs[letter]);
  })
  return map;
}

export default class CalculationUniversalTrackerService {
  private utrs: UniversalTrackerPlain[] = [];

  constructor(private initiativeId: string, private utrIds: string[], private utrvs: Utrv[]) { }

  public async getUtrs(): Promise<UniversalTrackerPlain[]> {
    if (this.utrs.length === 0) {
      const combinedUtrIds = [
        ...this.utrIds,
        ...this.utrvs.filter(utrv => !!utrv).map(utrv => utrv?.universalTrackerId)
      ]
      this.utrs = await UniversalTracker.find({
        _id: {
          $in: combinedUtrIds
        },
      }).lean() as UniversalTrackerPlain[];
    }
    return this.utrs;
  }

  public getUtr(utrId: string) {
    return this.utrs.find(utr => String(utr._id) === utrId);
  }

  public async get(): Promise<(AggregatedUniversalTracker | UniversalTrackerValuePlain<string>)[]> {
    const utrs = await this.getUtrs();

    const utrCodeMap = new Map<string, UniversalTrackerPlain>();
    utrs.forEach(utr => utrCodeMap.set(utr.code, utr));

    const utrvs: (AggregatedUniversalTracker | UniversalTrackerValuePlain<string>)[] = [];

    utrs
      .filter(utr => utr.type === UtrType.Calculation && !!utr.calculation)
      .forEach(utr => {
        const utrv =
          (utr.calculation?.useDisaggregations === true)
            ?
            this.getAggregateUtrvByDisaggregations(utrCodeMap, utr)
            :
            this.getUtrv(utrCodeMap, utr);

        if (utrv) {
          utrvs.push(utrv);
        }
      });
    return utrvs;
  }

  private getAggregateUtrvByDisaggregations(
    utrCodeMap: Map<string, UniversalTrackerPlain>,
    utr: UniversalTrackerPlain,
  ): AggregatedUniversalTracker | UniversalTrackerValuePlain<string> | undefined {
    if (!utr.calculation) {
      return;
    }

    const formula = utr.calculation.formula;
    const variableUtrs = ensureMap(utr.calculation.variables);

    const uniqueDisaggregations = new Set<string>();
    variableUtrs.forEach((details) => {
      const variableUtr = utrCodeMap.get(details.code);
      if (!variableUtr) {
        return;
      }
      const utrv = this.utrvs.find(v => v && String(v.universalTrackerId) === String(variableUtr._id));
      if (utrv instanceof AggregatedUniversalTracker) {
        utrv.disaggregation?.forEach(d => {
          uniqueDisaggregations.add(String(d.initiativeId));
        });
      }
    });

    const variableA = variableUtrs.get("a");
    if (!uniqueDisaggregations.size || !variableA) {
      return this.getUtrv(utrCodeMap, utr);
    }

    const aggregatedUtrv = new AggregatedUniversalTracker(utr);
    uniqueDisaggregations.forEach(initiativeId => {
      const variables: { [key: string]: number | string | undefined } = {};
      variableUtrs.forEach((details, variable) => {
        variables[variable] = 0;
        const variableUtr = utrCodeMap.get(details.code);
        if (!variableUtr) {
          return;
        }
        let utrv = this.utrvs.find(v => v && String(v.universalTrackerId) === String(variableUtr._id));
        if (!utrv) {
          return;
        }
        if (utrv instanceof AggregatedUniversalTracker) {
          const disaggregatedUtrv = utrv.disaggregation.find(d => String(d.initiativeId) === initiativeId);
          if (disaggregatedUtrv) {
            variables[variable] = resolveVariableByUtrValueType(details, variableUtr, disaggregatedUtrv) ?? 0;
          }
        } else {
          variables[variable] = resolveVariableByUtrValueType(details, variableUtr, utrv) ?? 0;
        }
      });

      let value = undefined;
      try {
        value = calculateFormula(formula, variables)
      } catch (e) {
        wwgLogger.info(e, {
          formula,
          variables,
          initiativeId,
          utrCode: utr.code,
        });
      }

      aggregatedUtrv.add({
        initiativeId: initiativeId,
        universalTrackerId: utr._id,
        effectiveDate: new Date(),
        type: UtrValueType.Number,
        value: value
      });
    });

    return aggregatedUtrv;
  }

  private convertToTextTableRow(utrvId?: string | ObjectId, text?: string) {
    return [
      {
        code: 'utrvId',
        value: String(utrvId ?? '')
      },
      {
        code: 'text',
        value: text
      }
    ]
  }

  private reduceDataToTextTable = (
    currentUtrv: AggregatedUniversalTracker | UniversalTrackerValuePlain,
    variableUtr: UniversalTrackerPlain,
    variableA: ConfigurationVariableSetup
  ): RowData[][] => {
    const valueListCode = variableA.valueListCode;

    if ([UtrValueType.Text, UtrValueType.ValueList].includes(variableUtr.valueType as UtrValueType)) {
      if (currentUtrv instanceof AggregatedUniversalTracker) {
        return currentUtrv.disaggregation
          .filter(d => d.valueData?.data)
          .map(d => this.convertToTextTableRow(d._id, d.valueData?.data));
      } else {
        return currentUtrv.valueData?.data ? [this.convertToTextTableRow(currentUtrv._id, currentUtrv.valueData?.data)] : [];
      }
    }

    if ([UtrValueType.ValueListMulti].includes(variableUtr.valueType as UtrValueType)) {
      if (currentUtrv instanceof AggregatedUniversalTracker) {
        return currentUtrv.disaggregation.reduce<RowData[][]>((acc, d) => {
          acc.push(this.convertToTextTableRow(d._id, d.valueData?.data));
          return acc;
        }, []);
      } else {
        return currentUtrv.valueData?.data ? [currentUtrv.valueData?.data] : [];
      }
    }

    if (variableUtr.valueType !== UtrValueType.Table) {
      // Error?
      return [];
    }

    const data: RowData[][] = [];
    if (currentUtrv instanceof AggregatedUniversalTracker) {
      const aggregatedUtrv = new AggregatedUniversalTracker({
        ...variableUtr,
        valueAggregation: variableA.valueAggregation
      }, currentUtrv.disaggregation, UtrvType.Actual, AggregationMode.Combined);

      aggregatedUtrv.valueData?.table?.forEach((row) => {
        const utrvId = row.find(r => r.code === 'utrvId');
        const col = row.find(r => r.code === valueListCode);
        if (col?.value) {
          data.push(
            this.convertToTextTableRow(utrvId?.value, col.value)
          );
        }
      });
      return data;
    }

    currentUtrv?.valueData?.table?.forEach((row) => {
      const col = row.find(r => r.code === valueListCode);
      if (col?.value) {
        data.push(
          this.convertToTextTableRow(currentUtrv._id, col.value)
        );
      }
    });
    return data;
  }

  private getTextTableUtrv(
    utrCodeMap: Map<string, UniversalTrackerPlain>,
    utr: UniversalTrackerPlain,
  ): UniversalTrackerValuePlain<string> | undefined {
    if (!utr.calculation) {
      return;
    }

    const variableUtrs = ensureMap(utr.calculation.variables);
    const variableA = variableUtrs.get("a");
    if (!variableA) {
      return;
    }

    const variableUtr = utrCodeMap.get(variableA.code);
    const valueListCode = variableA.valueListCode;
    if (!variableUtr || !valueListCode) {
      return;
    }

    let currentUtrv = this.utrvs.find(v => v && String(v.universalTrackerId) === String(variableUtr._id));
    if (!currentUtrv) {
      return;
    }

    const utrv: UniversalTrackerValuePlain<string> = {
      universalTrackerId: String(utr._id),
      value: undefined,
      _id: undefined,
      effectiveDate: new Date(),
      history: [],
      lastUpdated: new Date(),
      initiativeId: this.initiativeId,
      status: ActionList.Verified,
      sourceType: SourceTypes.Aggregated,
      valueType: UtrValueType.Table,
      type: 'actual',
      evidenceRequired: false,
      verificationRequired: false,
      valueData: {
        table: this.reduceDataToTextTable(currentUtrv, variableUtr, variableA)
      }
    };
    return utrv;
  }

  private getUtrv(
    utrCodeMap: Map<string, UniversalTrackerPlain>,
    utr: UniversalTrackerPlain,
  ): UniversalTrackerValuePlain<string> | undefined {
    if (!utr.calculation) {
      return;
    }

    const variableUtrs = ensureMap(utr.calculation.variables);
    const variableA = variableUtrs.get("a");
    if (!variableA) {
      return;
    }

    const isTextOutput = utr.valueType === UtrValueType.Text;
    if (isTextOutput) {
      return this.getTextTableUtrv(utrCodeMap, utr);
    }

    const variables: { [key: string]: number | string | undefined } = {};
    variableUtrs.forEach((details, variable) => {
      const variableUtr = utrCodeMap.get(details.code);
      if (!variableUtr) {
        return;
      }
      let utrv;
      if (variableUtr.calculation?.useDisaggregations === true) {
        utrv = this.getAggregateUtrvByDisaggregations(utrCodeMap, variableUtr);
      } else {
        utrv = this.utrvs.find(v => v && String(v.universalTrackerId) === String(variableUtr._id));
      }
      if (!utrv) {
        return;
      }
      variables[variable] = resolveVariableByUtrValueType(details, variableUtr, utrv) ?? 0;
    });


    let value = undefined;
    let valueData = undefined;
    try {
      value = calculateFormula(utr.calculation.formula, variables)
    } catch (e) {
      if (!isNAError(e) && !isNRError(e)) {
        wwgLogger.info(e, {
          formula: utr.calculation.formula,
          variables,
          utrCode: utr.code,
        });
      }
    }

    const utrv: UniversalTrackerValuePlain<string> = {
      universalTrackerId: String(utr._id),
      value: value,
      _id: undefined,
      effectiveDate: new Date(),
      history: [],
      lastUpdated: new Date(),
      initiativeId: this.initiativeId,
      status: ActionList.Verified,
      sourceType: SourceTypes.Aggregated,
      type: 'actual',
      evidenceRequired: false,
      verificationRequired: false,
      valueData: valueData
    };
    return utrv;
  }

  private static createUniversalTracker = async (name: string, valueType: UtrValueType, calculation: Calculation, initiativeId: string) => {
    const objectId = new ObjectId().toString();
    const code = `${name.toLowerCase().replace(/ /g, '-').replace(/[^a-z0-9\-]/gmi, '').substr(0, 30)}-${objectId}`;
    const utr = new UniversalTracker({
      code: code,
      name: name,
      valueLabel: name,
      type: UtrType.Calculation,
      valueType: valueType,
      ownerId: initiativeId,
      calculation: calculation
    });

    await utr.save();
    return utr;
  }

  private static async getPercentageOfCountUtr(d: MetricSubmitData, initiativeId: string, utrData: MetricSubmitDataUtr) {

    const calculation: Calculation = {
      variables: new Map(),
      formula: '100*{a}/{b}'
    };
    calculation.variables.set('a', {
      code: utrData.utr.code,
      valueAggregation: ValueAggregation.ValueListCountAggregator,
      valueListCode: utrData.isTable ? utrData.valueListCode : utrData.valueListMatch
    });

    calculation.variables.set('b', {
      code: utrData.utr.code,
      valueAggregation: ValueAggregation.ValueCountAggregator,
    });

    return await CalculationUniversalTrackerService.createUniversalTracker(d.name, UtrValueType.Percentage, calculation, initiativeId);
  }

  private static async getProportionUtr(d: PercentageMetricSubmitData, initiativeId: string, numeratorUtrData: MetricSubmitDataUtr, denominatorUtrData?: MetricSubmitDataUtr, valueAggregation?: ValueAggregation) {
    let sumValue = !denominatorUtrData ? '1' : '{b}';
    const calculationNumerator: Calculation = {
      variables: new Map(),
      useDisaggregations: true,
      formula: `resolveString("{a}","${numeratorUtrData.valueListMatch}",${sumValue},0)`
    };
    calculationNumerator.variables.set('a', {
      code: numeratorUtrData.utr.code,
      valueListCode: numeratorUtrData.valueListCode,
      valueAggregation: valueAggregation
    });
    if (denominatorUtrData) {
      if (!CalculationUniversalTrackerService.willUtrResolveToNumber(denominatorUtrData)) {
        throw new UserError(`UniversalTracker with code ${denominatorUtrData.utr.code} and valueListCode ${denominatorUtrData.valueListCode} will not resolve to a number (1)`);
      }
      calculationNumerator.variables.set('b', {
        code: denominatorUtrData.utr.code,
        valueListCode: denominatorUtrData.valueListCode
      });
    }
    return await CalculationUniversalTrackerService.createUniversalTracker(`${d.name} (Proportion Numerator)`, UtrValueType.Number, calculationNumerator, initiativeId);
  }

  private static async getNumeratorUtr(d: PercentageMetricSubmitData, initiativeId: string, primaryUtrData: MetricSubmitDataUtr, secondaryUtrData?: MetricSubmitDataUtr, valueAggregation?: ValueAggregation) {
    if (primaryUtrData.isValueList) {
      return await CalculationUniversalTrackerService.getProportionUtr(d, initiativeId, primaryUtrData, secondaryUtrData, valueAggregation);
    }

    if (primaryUtrData.isTable && primaryUtrData.columnType) {
      const isColumnValueList = [ColumnType.Text, ColumnType.ValueList, ColumnType.ValueListMulti].includes(primaryUtrData.columnType);
      if (isColumnValueList) {
        return await CalculationUniversalTrackerService.getProportionUtr(d, initiativeId, primaryUtrData, secondaryUtrData, valueAggregation);
      }
    }

    return await CalculationUniversalTrackerService.getTotalUtr(d, initiativeId, UtrValueType.Number, primaryUtrData);
  }

  private static willUtrResolveToNumber = (utrData?: MetricSubmitDataUtr) => {
    if (!utrData?.utr) {
      return false;
    }

    if ([UtrValueType.Number].includes(utrData.utr.valueType as UtrValueType)) {
      return true;
    }

    if (utrData.isTable) {
      const tableColumn = utrData.utr.valueValidation?.table?.columns.find(c => c.code === utrData.valueListCode);
      if (tableColumn?.type === ColumnType.Number) {
        return true;
      }
    }
    return false;
  };

  private static async getPercentageUtr(d: PercentageMetricSubmitData, initiativeId: string, newUtrValueType: UtrValueType, numeratorCode: string,
    denominatorUtrData: MetricSubmitDataUtr, valueAggregation?: ValueAggregation) {

    if (!valueAggregation && !CalculationUniversalTrackerService.willUtrResolveToNumber(denominatorUtrData)) {
      throw new UserError(`UniversalTracker with code ${denominatorUtrData.utr.code} and valueListCode ${denominatorUtrData.valueListCode} will not resolve to a number (2)`);
    }

    const calculation: Calculation = {
      variables: new Map(),
      formula: '100*{a}/{b}',
    };
    calculation.variables.set('a', {
      code: numeratorCode,
    });
    calculation.variables.set('b', {
      code: denominatorUtrData.utr.code,
      valueListCode: valueAggregation ? undefined : denominatorUtrData.valueListCode,
      valueAggregation: valueAggregation
    });

    return await CalculationUniversalTrackerService.createUniversalTracker(d.name, newUtrValueType, calculation, initiativeId);
  }

  private static async getPercentageDirectUtr(
    d: PercentageMetricSubmitData,
    initiativeId: string,
    newUtrValueType: UtrValueType,
    numeratorUtrData: MetricSubmitDataUtr,
    denominatorUtrData: MetricSubmitDataUtr,
    valueAggregation?: ValueAggregation
  ) {

    if (!valueAggregation && !CalculationUniversalTrackerService.willUtrResolveToNumber(denominatorUtrData)) {
      throw new UserError(`UniversalTracker with code ${denominatorUtrData.utr.code} and valueListCode ${denominatorUtrData.valueListCode} will not resolve to a number (2)`);
    }

    const calculation: Calculation = {
      variables: new Map(),
      formula: '100*{a}/{b}',
    };
    calculation.variables.set('a', {
      code: numeratorUtrData.utr.code,
      valueListCode: valueAggregation ? undefined : numeratorUtrData.valueListCode,
      valueAggregation: valueAggregation
    });
    calculation.variables.set('b', {
      code: denominatorUtrData.utr.code,
      valueListCode: valueAggregation ? undefined : denominatorUtrData.valueListCode,
      valueAggregation: valueAggregation
    });

    return await CalculationUniversalTrackerService.createUniversalTracker(d.name, newUtrValueType, calculation, initiativeId);
  }

  private static async getTextUtr(d: MetricSubmitData, initiativeId: string, newUtrValueType: UtrValueType, utrData: MetricSubmitDataUtr): Promise<UniversalTrackerPlain> {
    const isTextType = [UtrValueType.Text, UtrValueType.ValueList, UtrValueType.ValueListMulti].includes(utrData.utr.valueType as UtrValueType);
    if (isTextType) {
      return UniversalTracker.findOne({ code: d.primaryUtrCode }).orFail().lean() as Promise<UniversalTrackerPlain>;
    }

    const isTextColumnType = utrData.isTable && utrData.columnType && [ColumnType.Text, ColumnType.ValueList, ColumnType.ValueListMulti].includes(utrData.columnType);
    if (isTextColumnType) {
      const calculation: Calculation = {
        variables: new Map(),
        formula: '{a}',
        useDisaggregations: false
      };
      calculation.variables.set('a',
        {
          code: utrData.utr.code,
          valueAggregation: ValueAggregation.TableConcatenationAggregator,
          valueListCode: utrData.valueListCode
        }
      );

      return CalculationUniversalTrackerService.createUniversalTracker(d.name, newUtrValueType, calculation, initiativeId);
    }

    throw new UserError(`UniversalTracker with code ${utrData.utr.code} ${utrData.valueListCode ? `and valueListCode ${utrData.valueListCode}` : ''} will not resolve to text`);
  }

  private static async getTotalUtr(d: MetricSubmitData, initiativeId: string, newUtrValueType: UtrValueType, utrData: MetricSubmitDataUtr) {
    if (utrData.isTable && utrData.columnType !== ColumnType.Number) {
      return CalculationUniversalTrackerService.getCountUtr(d, initiativeId, newUtrValueType, utrData);
    }

    const calculation: Calculation = {
      variables: new Map(),
      formula: '{a}',
      useDisaggregations: utrData.isTable
    };
    calculation.variables.set('a',
      {
        code: utrData.utr.code,
        valueAggregation: utrData.isTable ? undefined : ValueAggregation.ValueListCountAggregator,
        valueListCode: utrData.isTable ? utrData.valueListCode : utrData.valueListMatch
      }
    );

    return await CalculationUniversalTrackerService.createUniversalTracker(d.name, newUtrValueType, calculation, initiativeId);
  }

  private static async getCountUtr(d: MetricSubmitData, initiativeId: string, newUtrValueType: UtrValueType, utrData: MetricSubmitDataUtr) {
    const calculation: Calculation = {
      variables: new Map(),
      useDisaggregations: true,
      formula: `resolveString("{a}","${utrData.valueListMatch}",1,0)`
    };
    calculation.variables.set('a',
      {
        code: utrData.utr.code,
        valueListCode: utrData.valueListCode
      }
    );

    return await CalculationUniversalTrackerService.createUniversalTracker(d.name, newUtrValueType, calculation, initiativeId);
  }

  static async createCombinedUtrs(d: TextMetricSubmitData | TotalMetricSubmitData | PercentageMetricSubmitData, initiativeId: string): Promise<UniversalTrackerPlain[]> {

    const primaryUtrData = await CalculationUniversalTrackerService.parseUtrData(d.primaryUtrCode, d.primaryValueListCode, d.primaryColumnCode);
    if (primaryUtrData.utr.valueType === UtrValueType.Number || primaryUtrData.utr.valueType === UtrValueType.Percentage) {
      return [primaryUtrData.utr]
    }
    if (d.showAs === 'total') {
      return [
        await CalculationUniversalTrackerService.getTotalUtr(d, initiativeId, UtrValueType.Number, primaryUtrData)
      ];
    }

    if (d.showAs === 'text') {
      return [
        await CalculationUniversalTrackerService.getTextUtr(d, initiativeId, UtrValueType.Text, primaryUtrData)
      ];
    }

    if (d.percentageOf === 'count') {
      if (primaryUtrData.isTable) {
        const numeratorUtr = await CalculationUniversalTrackerService.getNumeratorUtr(d, initiativeId, primaryUtrData);
        const percentageUtr = await CalculationUniversalTrackerService.getPercentageUtr(d, initiativeId, UtrValueType.Percentage, numeratorUtr.code, primaryUtrData, ValueAggregation.ValueCountAggregator);
        return [
          percentageUtr,
          numeratorUtr
        ];
      }

      return [
        await CalculationUniversalTrackerService.getPercentageOfCountUtr(d, initiativeId, primaryUtrData)
      ];
    }

    if ('secondaryUtrCode' in d) {
      const secondaryUtrData = await CalculationUniversalTrackerService.parseUtrData(d.secondaryUtrCode, d.secondaryValueListCode, d.secondaryColumnCode, primaryUtrData.utr);
      let valueAggregation = undefined;
      if (secondaryUtrData.isTable) {
        if (primaryUtrData.columnType === ColumnType.Number && secondaryUtrData.columnType === ColumnType.Number) {
          return [
            await this.getPercentageDirectUtr(
              d,
              initiativeId,
              UtrValueType.Percentage,
              primaryUtrData,
              secondaryUtrData,
              valueAggregation
            )
          ]
        }

        const isColumnValueList = [ColumnType.Text, ColumnType.ValueList, ColumnType.ValueListMulti].includes(secondaryUtrData.columnType as ColumnType);
        if (isColumnValueList) {
          valueAggregation = ValueAggregation.ValueSumAggregator;
        }
      }

      const numeratorUtr = await CalculationUniversalTrackerService.getNumeratorUtr(d, initiativeId, primaryUtrData, secondaryUtrData, valueAggregation);
      const percentageUtr = await CalculationUniversalTrackerService.getPercentageUtr(d, initiativeId, UtrValueType.Percentage, numeratorUtr.code, secondaryUtrData, valueAggregation);
      return [
        percentageUtr,
        numeratorUtr
      ];
    }

    console.log(`Unhandled MetricSubmitData: ${JSON.stringify(d)}`);
    return [];
  }

  private static async parseUtrData(utrCode: string, valueListCode: string | undefined, columnCode: string | undefined, existingUtr?: UniversalTrackerPlain): Promise<MetricSubmitDataUtr> {
    const isSameUtr = existingUtr?.code === utrCode;
    const utr = isSameUtr
      ? existingUtr
      : ((await UniversalTracker.findOne({ code: utrCode }).lean()) as UniversalTrackerPlain | null);

    if (!utr) {
      throw new UserError(`Could not find UniversalTracker with code ${utrCode}`);
    }

    const valueType = utr?.valueType as UtrValueType;
    const isTable = [UtrValueType.Table].includes(valueType);
    const isValueList = [UtrValueType.ValueList, UtrValueType.ValueListMulti].includes(valueType);

    let columnType = undefined;
    if (isTable) {
      const tableColumn = utr.valueValidation?.table?.columns.find(c => c.code === columnCode);
      if (tableColumn) {
        columnType = tableColumn?.type as ColumnType;
      }
    }

    return {
      utr,
      isTable,
      isValueList,
      valueListCode: isTable ? columnCode : undefined,
      valueListMatch: valueListCode,
      columnType
    }
  }

  static async getRecursiveUtrs(utrIds: (ObjectId | string)[]) {
    if (utrIds.length === 0) {
      return [];
    }
    const utrs = await UniversalTracker.find({
      _id: {
        $in: utrIds.map(id => new ObjectId(id))
      }
    }).lean().exec();

    const variableCodes: string[] = [];
    for (const utr of utrs) {
      if (utr.calculation?.variables) {
        ensureMap(utr.calculation?.variables).forEach(v => {
          variableCodes.push(v.code);
        });
      }
    }

    const variables = await UniversalTracker.find({
      code: { $in: variableCodes }
    }).lean() as UniversalTrackerPlain[];
    const variableUtrs = new Set(variables);
    const subCalculationVariables = Array.from(variableUtrs).filter(v => v.type === UtrType.Calculation);
    if (subCalculationVariables.length > 0) {
      const childVariables = await CalculationUniversalTrackerService.getRecursiveUtrs(subCalculationVariables.map(v => String(v._id)));
      childVariables.forEach(c => variableUtrs.add(c));
    }
    return [...variableUtrs];
  }

  static async deprecateUtrById(utrId: ObjectId | string, initiativeId: ObjectId | string) {
    const checkUtr = await UniversalTracker.findOne({
      _id: utrId,
      type: UtrType.Calculation
    }).exec();
    return CalculationUniversalTrackerService.deprecateUtr(checkUtr, initiativeId);
  }

  static async deprecateUtrByCode(utrCode: string, initiativeId: ObjectId | string) {
    const checkUtr = await UniversalTracker.findOne({
      code: utrCode,
      type: UtrType.Calculation
    }).exec();
    return CalculationUniversalTrackerService.deprecateUtr(checkUtr, initiativeId);
  }

  static async deprecateUtr(checkUtr: UniversalTrackerModel | null, initiativeId: ObjectId | string) {
    // If it is not used anywhere, then just delete the whole thing, or it will become orphaned
    if (!checkUtr || checkUtr.type !== UtrType.Calculation) {
      return;
    }

    const calculatedUtrUsage = await Initiative.find({
      _id: {
        $ne: initiativeId ?? undefined // Initiative unlinking is not saved yet
      },
      'linkedUniversalTrackers.universalTrackerId': checkUtr._id
    }).lean();

    if (calculatedUtrUsage && calculatedUtrUsage.length > 0) {
      return; // Still referenced
    }

    const calculatedUtrCustomReport = await CustomReport.find({
      'metrics.utrId': checkUtr._id
    }).lean();

    if (calculatedUtrCustomReport.length > 0) {
      return; // Still referenced
    }

    const referencedUtrCodes = checkUtr.calculation?.variables ?
      Array.from(checkUtr.calculation?.variables).map(([, config]) => config.code)
      : undefined;

    if (referencedUtrCodes && referencedUtrCodes.length > 0) {
      await Promise.all(
        referencedUtrCodes.map(code => CalculationUniversalTrackerService.deprecateUtrByCode(code, initiativeId))
      );
    }

    const utrId = checkUtr._id;
    await UniversalTracker.findByIdAndDelete(utrId);
    await UniversalTrackerValue.deleteMany({
      universalTrackerId: utrId
    });
  }
}
