/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { UniversalTrackerPlain } from "../../../models/universalTracker";
import { NotApplicableTypes, UniversalTrackerValuePlain } from "../../../models/universalTrackerValue";
import {
  AggregationMode,
  ColumnAggregationCompatibility,
  ColumnType,
  ColumnValueAggregation,
  UtrValueType,
  ValueAggregation,
  ValueAggregationChildrenCompatibility,
  ValueAggregationSiblingsCompatibility,
} from '../../../models/public/universalTrackerType';
import { RowData, ValueData } from '../../../models/public/universalTrackerValueType';
import { DisaggregationUniversalTrackerValueFields } from "./AggregatedUniversalTracker";
import { normalizeText } from '../../../util/string';

export type UtrValueFields = Pick<UniversalTrackerValuePlain,
  'value' |
  'valueData' |
  'effectiveDate' |
  'assuranceStatus' |
  'universalTrackerId' |
  'type' |
  'period'
>

export interface AggregatorValueFields extends UtrValueFields {
  utr: UniversalTrackerPlain;
  /** @description - Count of UTRVs added to the aggregation */
  aggregationCount: number;

  /** @description - Count of rows in the table for utrvs */
  tableRowCount: number;

  /** @description - Count of columns in the table for utrvs */
  tableColumnCount: { [code: string]: number | undefined };

  /** @description - Count of columns in the table for utrvs grouped by group key */
  tableGroupColumnCount: { [groupKey: string]: { [code: string]: number | undefined } | undefined };

  /** @description - Count of rows in the table for utrvs grouped by group key */
  tableGroupRowCount: { [groupKey: string]: number | undefined };

  valueData?: ValueData;
  disaggregation: DisaggregationUniversalTrackerValueFields[];
}

export type AggregatorFn = (acc: AggregatorValueFields, utrv: DisaggregationUniversalTrackerValueFields) => AggregatorValueFields

type ColumnAggregatorFn = (value: number | string, col: RowData, acc: AggregatorValueFields, utrv: UtrValueFields, averageCounter: number) => number | string;

const recalculateAverage = (existing: number, aggregate: number, count: number) => {
  return ((existing * count) + aggregate) / (count + 1);
};


const columnWithSetValue = (col: RowData | undefined): col is RowData => {
  return typeof col === 'object' && col.value !== undefined;
}

class Aggregators {
  // @TODO - what to do if NA or NR
  static valueSumAggregator: AggregatorFn = (acc, utrv) => {
    acc.value = (acc.value || 0) + (utrv.value || 0);
    return acc;
  }

  static valueAverageAggregator: AggregatorFn = (acc, utrv) => {
    if (utrv.valueData?.notApplicableType === NotApplicableTypes.NA) {
      return acc;
    }
    acc.value = recalculateAverage(acc.value || 0, utrv.value || 0, acc.aggregationCount);
    return acc;
  }

  static valueCountAggregator: AggregatorFn = (acc, utrv) => {
    if (utrv.valueData?.notApplicableType === NotApplicableTypes.NA) {
      return acc;
    }
    acc.value = (acc.value ?? 0) + 1;
    return acc;
  }

  static valueConcatenateAggregator: AggregatorFn = (acc, utrv) => {
    if (!utrv.valueData?.data || typeof utrv.valueData.data !== 'string') {
      return acc;
    }

    if (!acc.valueData?.data) {
      acc.valueData = {
        data: utrv.valueData.data
      };
      return acc;
    }
    acc.valueData.data = acc.valueData.data.concat(`, ${utrv.valueData.data}`);
    return acc;
  }

  static numericValueListAverageAggregator: AggregatorFn = (acc, utrv) => {
    if (typeof utrv.valueData?.data !== 'object') {
      return acc;
    }

    Object.entries(utrv.valueData.data).forEach(([code, v]) => {
      const value = Number(v || 0);
      if (acc.valueData?.data === undefined) {
        acc.valueData = {
          data: {},
        };
      }

      acc.valueData.data[code] = recalculateAverage(Number(acc.valueData.data[code] || 0), value, acc.aggregationCount);
    });

    const utrvValueListCodes = Object.keys(utrv.valueData?.data);
    // find codes that are missing in the current utrv answer to recalculate average of each code for combined utrv (acc)
    const missingCodes = Object.keys(acc.valueData?.data).filter((k) => utrvValueListCodes.indexOf(k) === -1);
    missingCodes.forEach((code) => {
      if (acc.valueData?.data) {
        acc.valueData.data[code] = recalculateAverage(acc.valueData.data[code], 0, acc.aggregationCount);
      }
    });

    acc.value = recalculateAverage(acc.value || 0, utrv.value || 0, acc.aggregationCount);

    return acc;
  }

  static numericValueListSumAggregator: AggregatorFn = (acc, utrv) => {
    if (typeof utrv.valueData?.data !== 'object') {
      return acc;
    }
    Object.entries(utrv.valueData.data).forEach(([code, v]) => {
      const value = Number(v);
      if (acc.valueData === undefined) {
        acc.valueData = {
          data: {}
        }
      }

      if (acc.valueData.data === undefined) {
        acc.valueData.data = {}
      }

      if (acc.valueData.data[code] === undefined) {
        acc.valueData.data[code] = value;
      } else {
        acc.valueData.data[code] = Number(acc.valueData.data[code]) + value;
      }
    });

    acc.value = (acc.value || 0) + (utrv.value || 0);

    return acc;
  }

  static textCountAggregator: AggregatorFn = (acc, utrv) => {
    return Aggregators.valueListCountAggregator(acc, utrv);
  }

  static valueListCountAggregator: AggregatorFn = (acc, utrv) => {
    if (!utrv.valueData?.data) {
      return acc;
    }

    const values = Array.isArray(utrv.valueData.data) ? utrv.valueData.data : [utrv.valueData.data];
    values.forEach(v => {
      if (typeof v === "string") {
        if (acc.valueData === undefined) {
          acc.valueData = {
            data: {}
          }
        }
        if (!acc.valueData.data[v]) {
          acc.valueData.data[v] = 0
        }
        acc.valueData.data[v] = Number(acc.valueData.data[v]) + 1;
      }
    })

    return acc;
  }

  static latestAggregator: AggregatorFn = (acc, utrv) => {
    if (!acc.effectiveDate || utrv.effectiveDate >= acc.effectiveDate) {
      acc.effectiveDate = utrv.effectiveDate;
      acc.value = utrv.value;
      if (utrv.valueData) {
        if (acc.valueData === undefined) {
          acc.valueData = {
            data: {}
          }
        }
        acc.valueData.data = utrv.valueData.data;
        acc.valueData.table = utrv.valueData.table;
        acc.valueData.notApplicableType = utrv.valueData.notApplicableType;
      }
    }
    return acc;
  }

  static tableAggregator: AggregatorFn = (acc, utrv) => {
    const tableDefinition = acc.utr.valueValidation?.table
    const tableData = utrv.valueData?.table;
    if (!tableData || !tableDefinition) {
      return acc;
    }

    const accRow: { [key: string]: string | number } = {};
    // Prefill initial values with current reducer accumulated values
    tableDefinition.columns.forEach(({ code }) => {
      const col = acc?.valueData?.table?.[0].find((column) => column.code === code);
      if (col?.value) {
        accRow[code] = col?.value;
      }
    });

    tableData.reduce((rowAcc, row) => {
      tableDefinition.columns.forEach(({ code, type, valueAggregation }) => {

        const col = row.find((column) => column.code === code);
        if (!columnWithSetValue(col)) {
          // This quite inconsistent, since we now commonly populate all columns with without value
          // therefore there is big difference between missing column vs empty column
          return;
        }

        const currentCounter = acc.tableColumnCount[code] ?? 0;

        const fn: ColumnAggregatorFn = getAggregatorByColumn(type as ColumnType, valueAggregation);
        rowAcc[code] = fn(rowAcc[code], col, acc, utrv, currentCounter);
        acc.tableColumnCount[code] = currentCounter + 1;
      });

      acc.tableRowCount += 1;

      return rowAcc;
    }, accRow);

    const cols: RowData[] = tableDefinition.columns
      .filter(({ code }) => accRow[code] !== undefined)
      .map(({ code }) => ({
        code: code,
        value: accRow[code]
      }));

    acc.valueData = {
      table: [
        cols
      ]
    }

    return acc;
  }

  // Append new rows, and add source utrvId
  static tableConcatenationAggregator: AggregatorFn = (acc, utrv) => {
    const tableDefinition = acc.utr.valueValidation?.table
    const tableData = utrv.valueData?.table;
    if (!tableData || !tableDefinition) {
      return acc;
    }

    if (!acc.valueData) {
      acc.valueData = {};
    }
    if (!acc.valueData.table) {
      acc.valueData.table = [];
    }

    tableData.forEach(row => acc.valueData?.table?.push([
      {
        code: 'utrvId',
        value: utrv._id
      },
      ...row
    ]));
    return acc;
  }

  // Group rows by a key, and aggregate the values
  static tableRowGroupAggregator: AggregatorFn = (acc, utrv) => {
    const tableDefinition = acc.utr.valueValidation?.table
    const tableData = utrv.valueData?.table;
    const groupColumns = acc.utr.valueValidation?.table?.aggregation?.columns.map(c => c.code);
    if (!tableData || !tableDefinition || !groupColumns) {
      return acc;
    }

    const groupKeyGenerator = (row: RowData[]) => {
      return groupColumns
        .map((c) => {
          const rawValue = row.find((r) => r.code === c)?.value;
          return normalizeText(rawValue, { lowercase: true });
        })
        .join('-');
    };

    const groupedData: { [key: string]: { [code: string]: number | string } } = {};

    // We process this again???
    (acc.valueData?.table || []).forEach((row) => {
      const key = groupKeyGenerator(row);

      if (!groupedData[key]) {
        groupedData[key] = {};
      }

      tableDefinition.columns.forEach(({ code, type, valueAggregation }) => {
        const col = row.find((column) => column.code === code);
        if (!columnWithSetValue(col)) {
          return;
        }

        const fn: ColumnAggregatorFn = getAggregatorByColumn(type as ColumnType, valueAggregation);
        const value = type === ColumnType.Text ? groupedData[key][code] ?? col.value : groupedData[key][code];

        // Averages should have been calculated in the accumulator already,
        // we do not average again in this step, just grouping the data again.
        const defaultAverage = 0;

        groupedData[key][code] = fn(value, col, acc, utrv, defaultAverage);
      });
    });

    // New data should increase the counts
    tableData.forEach((row) => {
      const key = groupKeyGenerator(row);

      if (!groupedData[key]) {
        groupedData[key] = {};
      }

      tableDefinition.columns.forEach(({ code, type, valueAggregation }) => {
        const col = row.find((column) => column.code === code);
        if (!columnWithSetValue(col)) {
          return;
        }

        if (!acc.tableGroupColumnCount[key]) {
          acc.tableGroupColumnCount[key] = {};
        }

        const fn: ColumnAggregatorFn = getAggregatorByColumn(type as ColumnType, valueAggregation);
        const value = type === ColumnType.Text ? groupedData[key][code] ?? col.value : groupedData[key][code];
        const avgCounter = acc.tableGroupColumnCount[key][code] ?? 0;
        groupedData[key][code] = fn(value, col, acc, utrv, avgCounter);

        acc.tableGroupColumnCount[key][code] = avgCounter + 1;
      });

      acc.tableGroupRowCount[key] = (acc.tableGroupRowCount[key] ?? 0) + 1;
    });

    const newTableData: RowData[][] = Object.keys(groupedData).map(key => {
      return tableDefinition.columns.map(({ code }) => ({
        code: code,
        // normalize values of grouped columns by trimming extra spaces (e.g., 'Gasoline  Consumption ' -> 'Gasoline Consumption')
        value: groupColumns.includes(code) ? normalizeText(groupedData[key][code]) : groupedData[key][code],
      }));
    });

    acc.valueData = {
      table: newTableData
    };

    return acc;
  }

  static columnSumAggregator: ColumnAggregatorFn = (accValue, col) => {
    if (isNaN(col.value)) {
      return accValue; // Just ignore it, and we can maintain empty values if none of the values exist
    }

    const value = Number(col.value ?? 0);
    if (accValue === undefined) {
      return value;
    }
    return Number(accValue) + value;
  }

  static columnAverageAggregator: ColumnAggregatorFn = (accValue, col, _utrvAcc, _utrv, averageCounter) => {
    const value = isNaN(col.value) ? 0 : Number(col.value);
    return recalculateAverage(Number(accValue) || 0, value || 0, averageCounter);
  }

  static columnLatestAggregator: ColumnAggregatorFn = (accValue, col, utrvAcc, utrv) => {
    if (!utrvAcc.effectiveDate || utrv.effectiveDate >= utrvAcc.effectiveDate) {
      utrvAcc.effectiveDate = utrv.effectiveDate;
      return col.value;
    }
    return accValue;
  }

  static columnConcatenateAggregator: ColumnAggregatorFn = (accValue, col, utrvAcc, utrv) => {
    const concatenate = accValue ? String(accValue).split(', ') : [];
    concatenate.push(col.value);
    return concatenate.join(', ');
  }

  // Not implemented, types does nothing
  static emptyAggregator: AggregatorFn = (acc) => acc
  static columnEmptyAggregator: AggregatorFn = (acc) => acc
}

const isCompatible = (aggregationFn: string, compatibility: { default: ValueAggregation | ColumnValueAggregation, compatible?: (ValueAggregation | ColumnValueAggregation)[] }) => {
  const availableAggregators = Object.keys(Aggregators);
  if (!availableAggregators.includes(aggregationFn)) {
    return false;
  }
  if (!compatibility) {
    return false;
  }
  if (compatibility.default === aggregationFn) {
    return true;
  }
  if (compatibility.compatible?.includes(aggregationFn as ValueAggregation)) {
    return true;
  }
  return false;
}

// @TODO - should probably do this compatbility check at a UTRV level too, as there could be a disconnect with UTR
export function getAggregatorByUniversalTrackerFn(
  utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'>,
  valueAggregationMode: AggregationMode
): ValueAggregation {
  const isCombinedAggregation = valueAggregationMode === AggregationMode.Combined;
  const valueType = utr.valueType as UtrValueType;
  const compatibility = isCombinedAggregation
    ? ValueAggregationSiblingsCompatibility[valueType]
    : ValueAggregationChildrenCompatibility[valueType];

  // First check if there's a mode-specific override in aggregationConfig
  const modeKey = isCombinedAggregation ? AggregationMode.Combined : AggregationMode.Children;
  const modeSpecificAggregation = utr.aggregationConfig?.modes?.[modeKey];
  
  if (modeSpecificAggregation && isCompatible(modeSpecificAggregation, compatibility)) {
    return modeSpecificAggregation;
  }

  // Fall back to valueAggregation if no mode-specific config or if it's incompatible
  if (utr.valueAggregation && isCompatible(utr.valueAggregation, compatibility)) {
    return utr.valueAggregation;
  }

  // Finally fall back to system default
  return compatibility.default;
}

export function getAggregatorByUniversalTracker(
  utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'>,
  valueAggregationMode: AggregationMode
): AggregatorFn {
  const fnName = getAggregatorByUniversalTrackerFn(utr, valueAggregationMode);
  return Aggregators[fnName as keyof Aggregators];
}

function getAggregatorByColumn(type: ColumnType, valueAggregation?: ColumnValueAggregation) {
  if (valueAggregation) {
    const compatibility = ColumnAggregationCompatibility[type as ColumnType];
    if (valueAggregation && isCompatible(valueAggregation, compatibility)) {
      return Aggregators[valueAggregation as keyof Aggregators];
    }
  }

  const defaultAggregationFn = ColumnAggregationCompatibility[type].default;
  return Aggregators[defaultAggregationFn as keyof Aggregators];
}

// TODO: Should we add more check for new aggregation types? valueConcatenateAggregator, numericValueListAverageAggregator
export function getUtrValueTypeByAggregator(aggregatorFn: ValueAggregation, originalType = UtrValueType.Number) {
  switch (aggregatorFn) {
    case ValueAggregation.ValueSumAggregator:
    case ValueAggregation.ValueCountAggregator:
    case ValueAggregation.ValueAverageAggregator:
      return UtrValueType.Number;
    case ValueAggregation.ValueListCountAggregator:
    case ValueAggregation.TextCountAggregator:
    case ValueAggregation.NumericValueListSumAggregator:
      return UtrValueType.NumericValueList;
    case ValueAggregation.TableColumnAggregator:
    case ValueAggregation.TableConcatenationAggregator:
    case ValueAggregation.TableRowGroupAggregator:
      return UtrValueType.Table;
    case ValueAggregation.EmptyAggregator:
      return UtrValueType.Number;
    case ValueAggregation.LatestAggregator:
    default:
      return originalType;
  }
}
