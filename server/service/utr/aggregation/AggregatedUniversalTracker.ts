/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { UniversalTrackerPlain } from '../../../models/universalTracker';
import {
  NotApplicableTypes,
  SourceItem,
  UtrvAssuranceStatus,
  ValueHistory,
} from "../../../models/universalTrackerValue";
import {
  AggregatorFn,
  AggregatorValueFields,
  getAggregatorByUniversalTracker,
  getAggregatorByUniversalTrackerFn,
  UtrValueFields,
} from "./utrTypeAggregator";
import { AggregationMode } from "../../../models/public/universalTrackerType";
import { UtrValueType, ValueAggregation } from '../../../models/public/universalTrackerType';
import { ObjectId } from 'bson';
import { DataPeriods, UtrvType } from '../constants';
import { ValueData } from "../../../models/public/universalTrackerValueType";
import { wwgLogger } from "../../wwgLogger";
import ContextError from "../../../error/ContextError";


export interface DisaggregationUniversalTrackerValueFields extends UtrValueFields {
  initiativeId?: ObjectId | string;
  _id?: ObjectId | string;
  updatedHistory?: ValueHistory;
  latestHistory?: ValueHistory;
  status?: string;
  sourceItems?: SourceItem<ObjectId>[];
}

export class AggregatedUniversalTracker implements AggregatorValueFields {

  type: UtrvType;
  universalTrackerId: ObjectId;
  value: number | undefined;
  period?: DataPeriods;
  effectiveDate: Date = new Date(-1); // Set to beginning of time. Will get overwritten with first UTRV
  assuranceStatus?: UtrvAssuranceStatus | undefined;
  valueData?: ValueData;
  aggregationCount = 0;
  valueType: UtrValueType;
  disaggregation: DisaggregationUniversalTrackerValueFields[] = [];
  valueAggregation: ValueAggregation;
  sourceItems?: SourceItem<ObjectId>[];

  tableRowCount = 0;
  tableColumnCount: { [code: string]: number | undefined } = {};
  tableGroupColumnCount: { [groupKey: string]: { [code: string]: number | undefined } | undefined } = {};
  tableGroupRowCount: { [groupKey: string]: number | undefined } = {};

  missMatchCount = 0;

  private readonly aggregator: AggregatorFn;

  constructor(
    public utr: UniversalTrackerPlain,
    utrvs: DisaggregationUniversalTrackerValueFields[] = [],
    type = UtrvType.Actual,
    aggregationMode = AggregationMode.Children
  ) {
    this.universalTrackerId = utr._id;
    this.type = type;
    this.valueType = this.getValueType(utr.valueType as UtrValueType);

    this.aggregator = getAggregatorByUniversalTracker(utr, aggregationMode);
    this.valueAggregation = getAggregatorByUniversalTrackerFn(utr, aggregationMode);
    utrvs.forEach(utrv => this.add(utrv));
  }

  /**
   * At the moment treating NR and NA the same way
   * In the future, should penalize on NR but not on NA
   */
  private isNAorNR(utrv: DisaggregationUniversalTrackerValueFields): boolean {

    if ([UtrValueType.Number, UtrValueType.Percentage].includes(this.utr.valueType as UtrValueType)) {

      // Falling back to original numeric === undefined logic
      // Don't aggregate N/A or N/R or it will lower the averages on percentages
      if (utrv.value === undefined) {
        return true;
      }
    }

    const naType = utrv.valueData?.notApplicableType as NotApplicableTypes;
    return naType && [NotApplicableTypes.NA, NotApplicableTypes.NR].includes(naType)
  }

  public add(utrv: DisaggregationUniversalTrackerValueFields | AggregatedUniversalTracker): void {

    // Update sourceItems if utrv is aggregated utrv
    if (utrv.sourceItems) {
      if (!this.sourceItems) {
        this.sourceItems = [];
      }
      utrv.sourceItems.forEach(a => this.sourceItems?.push(a));
    }

    const newPeriod = utrv.period ?? DataPeriods.Yearly;

    // Date must be set for utrvs to avoid picking up the default beginning of time
    const date = new Date(utrv.effectiveDate)
    if (this.isLaterDate(date)) {
      this.effectiveDate = date;
    }

    if (!this.period) {
      this.period = newPeriod;
    } else if (this.period !== newPeriod && this.missMatchCount < 100) {
      // Limit logging to 100 per aggregation
      this.missMatchCount++;
      wwgLogger.info(new ContextError(`Trying to aggregate UTRVs with different periods: ${this.period} and ${newPeriod}.`, {
        utrvId: '_id' in utrv ? utrv._id?.toString() : undefined,
        utrvInitiativeId: 'initiativeId' in utrv ? utrv.initiativeId?.toString() : undefined,
        utrId: utrv.universalTrackerId?.toString(),
        debugMessage: `This is getting ignored for now, but should probably be restricted in the future`,
      }));
    }

    if (this.isNAorNR(utrv)) {
      if (this.aggregationCount === 0) {
        this.valueData = {
          notApplicableType: utrv.valueData?.notApplicableType
        }
      }
      return;
    }

    if (!(utrv instanceof AggregatedUniversalTracker)) {
      // Store the underlying utrvs so that we can use them in alternative aggregations (e.g. Custom targets)
      this.disaggregation.push(utrv);
    } else {
      if (utrv.disaggregation) {
        utrv.disaggregation.forEach(u => this.disaggregation.push(u));
      }
    }

    this.aggregator(this, utrv);
    this.assuranceStatus = this.assuranceStatus || utrv.assuranceStatus;
    this.aggregationCount += 1;
  }

  private isLaterDate(effectiveDate: Date) {
    return !this.effectiveDate || effectiveDate > this.effectiveDate;
  }

  private getValueType(valueType: UtrValueType) {
    return valueType;
  }
}
