/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import ContextError from '../../../error/ContextError';
import { type ObjectId } from 'bson';
import ScorecardFactory, { type Scorecard } from '../../scorecard/ScorecardFactory';
import Survey, { type SurveyModelPlain, SurveyType } from '../../../models/survey';
import UniversalTrackerValue, { type UniversalTrackerValuePlain } from '../../../models/universalTrackerValue';
import { type KeysEnum } from '../../../models/commonProperties';
import { type UniversalTrackerPlain } from '../../../models/universalTracker';
import { addDate } from '../../../util/date';
import { type ValueList } from '../../../models/public/valueList';
import moment from 'moment';
import { type UnitConfig } from '../../../types/units';

type HelperSurvey = Pick<SurveyModelPlain, '_id' | 'effectiveDate' | 'period' | 'fragmentUtrvs' | 'unitConfig'>;
const surveyProjection: KeysEnum<HelperSurvey> = {
  _id: 1,
  effectiveDate: 1,
  period: 1,
  fragmentUtrvs: 1,
  unitConfig: 1,
};

export type HelperUtr = Pick<
  UniversalTrackerPlain,
  | '_id'
  | 'code'
  | 'name'
  | 'valueLabel'
  | 'instructions'
  | 'valueType'
  | 'valueValidation'
  | 'type'
  | 'typeCode'
  | 'typeTags'
  | 'alternatives'
  | 'tags'
  | 'unit'
  | 'numberScale'
  | 'unitType'
> & {
  tableColumnValueListOptions?: ValueList[];
  valueListOptions?: ValueList;
};

const utrProjection: KeysEnum<Omit<HelperUtr, 'tableColumnValueListOptions' | 'valueListOptions'>> = {
  _id: 1,
  code: 1,
  valueType: 1,
  valueValidation: 1,
  type: 1,
  typeTags: 1,
  typeCode: 1,
  alternatives: 1,
  tags: 1,
  unit: 1,
  numberScale: 1,
  name: 1,
  valueLabel: 1,
  instructions: 1,
  unitType: 1,
};

export interface HelperUtrv<T = HelperUtr>
  extends Pick<
    UniversalTrackerValuePlain,
    '_id' | 'valueType' | 'universalTrackerId' | 'value' | 'valueData' | 'notes' | 'note' | 'unit' | 'numberScale' | 'status' | 'isPrivate'
  > {
  universalTracker?: T;
}

export interface HelperUtrvExtended<T = HelperUtr> extends HelperUtrv<T> {
  surveyUnitConfig?: UnitConfig;
}

const utrvProjection: KeysEnum<HelperUtrv> = {
  _id: 1,
  value: 1,
  valueType: 1,
  valueData: 1,
  universalTrackerId: 1,
  universalTracker: 1,
  note: 1,
  notes: 1,
  unit: 1,
  numberScale: 1,
  status: 1,
  isPrivate: 1,
};

export class PPTXTemplateSurveyCache {
  private survey: null | undefined | HelperSurvey = undefined;
  private surveyUtrvs: null | Map<string, HelperUtrv> = null;
  private scorecard: null | Scorecard = null;

  constructor(private surveyId: ObjectId, private initiativeId: ObjectId, private periodOffset: number = 0) {}

  public async getSurvey(): Promise<HelperSurvey | null> {
    if (this.survey !== undefined) {
      return this.survey;
    }

    if (!this.survey) {
      const targetSurvey = await Survey.findOne(
        {
          _id: this.surveyId,
          initiativeId: this.initiativeId,
        },
        surveyProjection
      ).lean();
      if (!targetSurvey) {
        throw new ContextError('Could not load target survey to generate PPTX report', {
          surveyId: this.surveyId,
        });
      }

      if (this.periodOffset === 0) {
        this.survey = targetSurvey;
        return this.survey;
      }

      const { period, effectiveDate } = targetSurvey;
      const previousSurveyEnd = addDate(effectiveDate, this.periodOffset, 'years');

      this.survey = await Survey.findOne(
        {
          type: SurveyType.Default,
          period,
          initiativeId: this.initiativeId,
          effectiveDate: {
            $gte: moment.utc(previousSurveyEnd).startOf('month').startOf('day').toDate(),
            $lte: moment.utc(previousSurveyEnd).endOf('month').endOf('day').toDate(),
          },
        },
        surveyProjection
      ).lean();
    }

    return this.survey ?? null;
  }

  public async getScorecard(): Promise<Scorecard> {
    if (this.scorecard) {
      return this.scorecard;
    }
    const scorecardFactory = new ScorecardFactory();
    const scorecard = await scorecardFactory.getBySurveyId(this.surveyId);
    this.scorecard = scorecard;
    return scorecard;
  }

  public async getUnitConfig(): Promise<UnitConfig | undefined> {
    const survey = await this.getSurvey();
    if (!survey) {
      return;
    }
    return survey.unitConfig;
  }

  private async loadUtrvs() {
    if (this.surveyUtrvs !== null) {
      return;
    }
    const survey = await this.getSurvey();
    if (!survey) {
      return;
    }
    const utrvIds = survey.fragmentUtrvs;
    const utrvs: HelperUtrv[] = await UniversalTrackerValue.find(
      {
        _id: { $in: utrvIds },
      },
      utrvProjection
    )
      .populate({
        path: 'universalTracker',
        select: utrProjection,
        populate: [
          {
            path: 'valueListOptions',
          },
          {
            path: 'tableColumnValueListOptions',
          },
        ],
      })
      .lean()
      .exec();

    this.surveyUtrvs = new Map();
    utrvs.forEach((utrv) => {
      if (utrv.universalTracker) {
        this.surveyUtrvs?.set(utrv.universalTracker.code, utrv);
      }
    });
  }

  public async getUtrvsMap () {
    await this.loadUtrvs();
    return this.surveyUtrvs;
  }

  public async getUTRV(utrCode: string): Promise<HelperUtrv | null> {
    await this.loadUtrvs();
    if (!this.surveyUtrvs) {
      return null;
    }
    return this.surveyUtrvs.get(utrCode) ?? null;
  }
}
