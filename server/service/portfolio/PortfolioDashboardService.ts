import { ObjectId } from "bson";
import { RequesterType } from "../../models/dataShare";
import { InitiativePlain } from "../../models/initiative";
import InitiativeGroup from "../../models/initiativeGroup";
import {
  ExtendedDashboard,
  HistoricalUtrs,
  InsightDashboard,
  InsightDashboardPlain,
  InsightDashboardType,
  ToggleFilter
} from "../../models/insightDashboard";
import Survey, { BASED_SURVEY_TYPES, SurveyModelPlain } from "../../models/survey";
import UniversalTracker, {
  UniversalTrackerPlain
} from "../../models/universalTracker";
import {
  UniversalTrackerValuePlain,
  UtrvAssuranceStatus
} from "../../models/universalTrackerValue";
import {
  DataShareRepository,
  getDataShareRepository
} from "../../repository/DataShareRepository";
import { BreakdownFilter } from "./PortfolioPackUsageService";
import { PortfolioService } from "./PortfolioService";
import { SurveyQueryHelper } from "../survey/SurveyQueryHelper";
import { AggregatedUniversalTracker } from "../utr/aggregation/AggregatedUniversalTracker";
import {
  AggregationUniversalTrackerPlain,
  PreloadOptions
} from "../utr/aggregation/aggregatorDataService";
import { AggregationMode } from "../../models/public/universalTrackerType";
import { UtrvType } from "../utr/constants";
import {
  getUtrvFiltersFromDashboardFilters,
  shouldLoadScorecard
} from "../insight-dashboard/utils";
import ScorecardFactory from "../scorecard/ScorecardFactory";

// TODO: filter by period later

interface UtrvFilters
  extends Pick<PreloadOptions, "assuredOnly" | "isPublicOnly" | 'dateRange' | 'surveyType'> {
  utrIds: ObjectId[];
}

type SurveyFilters = Pick<
  BreakdownFilter,
  "initiativeIds" | "validatedScope" | "customMetricGroups"
>;

type SurveysWithUtrvs = Pick<
  SurveyModelPlain,
  "_id" | "scope" | "initiativeId"
> & {
  utrvs: UniversalTrackerValuePlain[];
};

type PortfolioPreloadOptions = PreloadOptions & {
  dataShareInitiativeId?: string;
}

export class PortfolioDashboardService {
  constructor(private dataShareRepo: DataShareRepository) {}

  public async populateUtrDataByPortfolioId({
    dashboard,
    portfolio,
    filters
  }: {
    dashboard: InsightDashboardPlain;
    portfolio: InitiativePlain;
    filters: Pick<PortfolioPreloadOptions, 'dateRange' | 'timeFrameType' | 'dataShareInitiativeId'>;
  }): Promise<ExtendedDashboard> {
    const { dataShareInitiativeId, ...additionalFilters } = filters;
    const utrCodes = Array.from(
      dashboard.items.reduce((acc, item) => {
        if (item.variables) {
          Object.values(item.variables).forEach((variable) =>
            acc.add(variable.code)
          );
        }

        return acc;
      }, new Set<string>())
    );

    const utrvFilters = getUtrvFiltersFromDashboardFilters({
      filters: dashboard.filters,
      additionalFilters
    });

    const utrsData = await this.getUtrsHistoricalData({
      filters: { ...utrvFilters, dataShareInitiativeId },
      portfolio,
      utrCodes
    });

    return {
      _id: dashboard._id,
      type: dashboard.type ?? InsightDashboardType.Custom,
      creatorId: dashboard.creatorId,
      initiativeId: dashboard.initiativeId,
      title: dashboard.title,
      filters: dashboard.filters,
      items: dashboard.items,
      share: dashboard.share,
      utrsData: utrsData
    };
  }

  public async populateScorecard(
    dashboard: ExtendedDashboard,
    portfolio: InitiativePlain
  ) {
    if (!shouldLoadScorecard(dashboard)) {
      return dashboard;
    }

    const { _id, permissionGroup, materiality } = portfolio;
    const scorecardFactory = new ScorecardFactory();
    const scorecardGroup = await scorecardFactory.getByInitiativeGroupId(
      portfolio.initiativeGroupId
    );
    return {
      ...dashboard,
      scorecard: {
        ...scorecardGroup,
        initiative: { _id, permissionGroup, materiality }
      }
    };
  }

  public async getUtrsHistoricalData({
    filters,
    portfolio,
    utrCodes
  }: {
    filters: Pick<
      PortfolioPreloadOptions,
      'assuredOnly' | 'isPublicOnly' | 'surveyType' | 'dateRange' | 'dataShareInitiativeId'
    >;
    portfolio: InitiativePlain;
    utrCodes: string[];
  }) {
    if (utrCodes.length === 0) {
      return [];
    }

    const utrs = await UniversalTracker.find({ code: { $in: utrCodes } })
      .lean()
      .exec() as UniversalTrackerPlain[];

    if (utrs.length === 0) {
      return [];
    }

    const { assuredOnly, isPublicOnly, dateRange, surveyType, dataShareInitiativeId } = filters;

    const utrvFilters = {
      assuredOnly,
      isPublicOnly,
      dateRange,
      surveyType,
      utrIds: utrs.map((utr) => utr._id)
    };

    const { companiesByScope, customMetricGroups } = await this.preloadByPortfolio(portfolio, dataShareInitiativeId);

    const utrvs = [];

    for (const grouping of Object.values(companiesByScope)) {
      const surveys = await this.getPortfolioSurveysWithUtrvs(
        {
          initiativeIds: grouping.initiativeIds,
          validatedScope: grouping.validatedScope,
          customMetricGroups
        },
        utrvFilters
      );
      const utrvsPerScopeGroup = surveys.map((survey) => survey.utrvs).flat();
      utrvs.push(...utrvsPerScopeGroup);
    }

    const utrsData = this.convertToUtrsData(utrs, utrvs);

    return utrsData;
  }

  private async preloadByPortfolio(portfolio: InitiativePlain, dataShareInitiativeId?: string) {
    const initiativeIds = !dataShareInitiativeId ? (await InitiativeGroup.findById(
      portfolio.initiativeGroupId
    )
      .orFail()
      .lean()
      .exec()).group.map((g) => g.initiativeId) : [new ObjectId(dataShareInitiativeId)];

    const activeDataShares = await this.dataShareRepo.findActiveDataShare({
      requesterType: RequesterType.Portfolio,
      requesterId: portfolio._id,
      initiativeId: {
        $in: initiativeIds.map((id) => new ObjectId(id))
      }
    });

    const customMetricGroups = await PortfolioService.getPortfolioMetricGroups(
      portfolio,
      []
    );

    const companiesByScope = await PortfolioService.getSharedScopesCompanies({
      requestedScope: undefined,
      activeDataShares
    });

    return { companiesByScope, customMetricGroups };
  }

  private convertToUtrsData(
    utrs: UniversalTrackerPlain[],
    utrvs: UniversalTrackerValuePlain[]
  ) {
    const utrMap = this.mergeUtrvsByUtrId(utrvs);

    return utrs.reduce((acc, utr) => {
      const utrvsByDate = utrMap.get(utr._id.toString());

      if (!utrvsByDate) {
        acc.push({ utr, utrvs: [] });
        return acc;
      }

      const aggregatedUtrvs = Array.from(utrvsByDate).reduce(
        (acc, [_, utrvs]) => {
          const aggregatedValues = new AggregatedUniversalTracker(
            utr as AggregationUniversalTrackerPlain,
            utrvs,
            UtrvType.Actual,
            AggregationMode.Combined
          );

          acc.push(aggregatedValues);
          return acc;
        },
        [] as AggregatedUniversalTracker[]
      );

      acc.push({ utr, utrvs: aggregatedUtrvs });
      return acc;
    }, [] as HistoricalUtrs[]);
  }

  private mergeUtrvsByUtrId(utrvs: UniversalTrackerValuePlain[]) {
    const utrMap = new Map<string, Map<string, UniversalTrackerValuePlain[]>>();

    utrvs.forEach((utrv) => {
      const utrId = utrv.universalTrackerId.toString();

      const utrvDate = utrv.effectiveDate;
      utrvDate.setUTCHours(0, 0, 0, 0);
      const effectiveDate = utrvDate.toISOString();

      const effectiveDateMap = utrMap.get(utrId);

      if (!effectiveDateMap) {
        return utrMap.set(utrId, new Map([[effectiveDate, [utrv]]]));
      }

      const utrvsPerEffectiveDate = effectiveDateMap.get(effectiveDate);

      if (!utrvsPerEffectiveDate) {
        return effectiveDateMap.set(effectiveDate, [utrv]);
      }

      return effectiveDateMap.set(effectiveDate, [
        ...utrvsPerEffectiveDate,
        utrv
      ]);
    });

    return utrMap;
  }

  private async getPortfolioSurveysWithUtrvs(
    filterOptions: SurveyFilters,
    utrvFilters: UtrvFilters
  ): Promise<SurveysWithUtrvs[]> {
    const { initiativeIds, validatedScope } = filterOptions;
    const { isPublicOnly, assuredOnly, dateRange, surveyType, utrIds } = utrvFilters;

    const utrvMatch: { [key: string]: object } = {
      "utrvs.universalTrackerId": { $in: utrIds }
    };

    if (isPublicOnly) {
      utrvMatch["utrvs.isPrivate"] = { $ne: true };
    }

    if (assuredOnly) {
      utrvMatch["utrvs.assuranceStatus"] = {
        $in: [UtrvAssuranceStatus.Completed, UtrvAssuranceStatus.CompletedOpen]
      };
    }

    if (dateRange) {
      const {startDate, endDate} = dateRange;
      utrvMatch['utrvs.effectiveDate'] = {
        ...(startDate && { $gte: new Date(startDate) }),
        ...(endDate && { $lte: new Date(endDate) }),
      };
    }

    return Survey.aggregate([
      {
        $match: {
          initiativeId: { $in: initiativeIds },
          completedDate: { $exists: true },
          deletedDate: { $exists: false },
          ...SurveyQueryHelper.toScopeMatch(validatedScope.scope),
          ...(surveyType ? { type: surveyType } : { type: { $in: BASED_SURVEY_TYPES } })
        }
      },
      {
        $addFields: {
          combinedUtrvs: {
            $concatArrays: [
              { $ifNull: ["$visibleUtrvs", []] },
              { $ifNull: ["$compositeUtrvs", []] }
            ]
          }
        }
      },
      {
        $lookup: {
          from: "universal-tracker-values",
          localField: "combinedUtrvs",
          foreignField: "_id",
          as: "utrvs"
        }
      },
      {
        $unwind: "$utrvs"
      },
      {
        $match: utrvMatch
      },
      {
        $group: {
          _id: "$_id",
          scope: { $first: "$scope" },
          initiativeId: { $first: "$initiativeId" },
          utrvs: { $push: "$utrvs" }
        }
      }
    ]).exec();
  }

  public async ensureOneDefaultDashboard({
    displayAsDefault,
    portfolioId,
    dashboardId,
  }: {
    displayAsDefault?: ToggleFilter;
    portfolioId: ObjectId;
    dashboardId: ObjectId;
  }) {
    if (!displayAsDefault || !displayAsDefault.enabled) {
      return;
    }

    await InsightDashboard.updateMany(
      {
        _id: { $ne: dashboardId },
        initiativeId: portfolioId,
        'filters.displayAsDefault.enabled': true,
      },
      {
        $set: {
          'filters.displayAsDefault.enabled': false,
        },
      }
    );
  }
}

let instance: PortfolioDashboardService;
export const getPortfolioDashboardService = () => {
  if (!instance) {
    instance = new PortfolioDashboardService(getDataShareRepository());
  }
  return instance;
};
