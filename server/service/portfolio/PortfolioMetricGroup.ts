/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import {
  getInitiativeRepository,
  InitiativeRepository
} from "../../repository/InitiativeRepository";
import {MetricGroupPlain} from "../../models/metricGroup";
import {
  getSurveyAggregator,
  SurveyAggregator
} from "../survey/SurveyAggregator";
import {
  getSurveyDataTransfer,
  SurveyDataTransfer
} from "../survey/SurveyDataTransfer";
import { UserPlain } from "../../models/user";
import { AggregatedUniversalTracker } from "../utr/aggregation/AggregatedUniversalTracker";
import { AggregationMode } from '../../models/public/universalTrackerType';
import { ActionList } from '../utr/constants';


export class PortfolioMetricGroup {

  constructor(
    private initRepo: InitiativeRepository,
    private surveyAggregator: SurveyAggregator,
    private surveyDataTransfer: SurveyDataTransfer,
  ) {
  }

  public async downloadAggregateGroupCsv(metricGroup: MetricGroupPlain, user: User<PERSON>lain) {
    const data: AggregatedUniversalTracker[] = await this.aggregateGroup(metricGroup);
    const utrvs = data.map(d => this.surveyAggregator.convertToUtrvExtended(d, metricGroup.initiativeId))
    return this.surveyDataTransfer.createFrom({ utrvs, valueLists: [] }, user)
  }

  private async aggregateGroup(metricGroup: MetricGroupPlain) {

    if (!metricGroup.share) {
      return []
    }

    const initiativeIds: ObjectId[] = [];
    for (const shareItem of metricGroup.share) {
      if (shareItem.acceptedDate) {
        initiativeIds.push(shareItem.initiativeId);
      }
    }

    const initiatives = await this.initRepo.getCompletedSurveyIds(initiativeIds, metricGroup);

    return this.surveyAggregator.aggregateSurveyIds({
      surveyIds: initiatives.map(i => i.surveys.map(s => s._id)).flat(),
      utrIds: metricGroup.universalTrackers,
      aggregationMode: AggregationMode.Children,
      // Portfolio metric group currently only works with verified utrvs
      utrvStatuses: [ActionList.Verified],
    })
  }
}

let instance: PortfolioMetricGroup;
export const getPortfolioMetricGroup = () => {
  if (!instance) {
    instance = new PortfolioMetricGroup(
      getInitiativeRepository(),
      getSurveyAggregator(),
      getSurveyDataTransfer(),
    );
  }
  return instance;
}
