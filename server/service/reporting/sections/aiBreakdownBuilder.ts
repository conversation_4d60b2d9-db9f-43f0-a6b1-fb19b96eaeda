import { createHeadlessEditor } from '@lexical/headless';
import { $createParagraphNode, $createTextNode, $getRoot } from 'lexical';
import { XbrlTracker } from '../XbrlTracker';
import { editorConfig } from '../utils';
import type { OutlineSection } from './utils';
import type { ObjectId } from 'bson';
import type { SectionBreakdown } from '../../report-document/types';
import { $createHeadingNode } from '@lexical/rich-text';
import type { UtrvData } from '../types';
import { buildStaticSection } from './staticBuilder';
import { $createListItemNode, $createListNode } from '@lexical/list';
import type { LexicalNode } from 'lexical/LexicalNode';

interface BuildAIBreakdownLexicalStateParams {
  reportId: ObjectId;
  reportType: string;
  sections: Record<string, OutlineSection>;
  sectionsData: Record<string, { relevantUtrvData: UtrvData[]; breakdown: SectionBreakdown } | undefined>;
}

export const buildAIBreakdownLexicalState = async (params: BuildAIBreakdownLexicalStateParams) => {
  const { sections, sectionsData } = params;

  const tracker = new XbrlTracker();

  // Use Lexical headless editor to build the state
  const editor = createHeadlessEditor(editorConfig);
  editor.update(
    () => {
      $getRoot().append(
        ...Object.entries(sections).flatMap(([sectionKey, outline]) => {
          const { relevantUtrvData, breakdown } = sectionsData[sectionKey] || {};
          return buildAIBreakdownSection({ tracker, outline, breakdown, relevantUtrvData });
        })
      );
    },
    { discrete: true }
  );

  return editor.getEditorState().toJSON();
};

interface BuildAIBreakdownSectionParams {
  tracker: XbrlTracker;
  outline: OutlineSection;
  breakdown?: SectionBreakdown;
  relevantUtrvData?: UtrvData[];
}

const buildAIBreakdownSection = (params: BuildAIBreakdownSectionParams): LexicalNode[] => {
  const { tracker, outline, breakdown, relevantUtrvData = [] } = params;

  if (!breakdown) {
    return buildStaticSection({ section: outline });
  }

  const nodes = [];

  const sectionHeading = $createHeadingNode('h2');
  sectionHeading.append($createTextNode(`📑 ${outline.heading}`));
  nodes.push(sectionHeading);

  const introductionParagraph = $createParagraphNode();
  introductionParagraph.append($createTextNode(breakdown.introduction));
  nodes.push(introductionParagraph);

  const summaryParagraph = $createParagraphNode();
  summaryParagraph.append($createTextNode(breakdown.summary));
  nodes.push(summaryParagraph);

  breakdown.xbrlTagSections.forEach(({ title, description, keyHighlights }) => {
    const subsectionHeading = $createHeadingNode('h3');
    subsectionHeading.append($createTextNode(title));

    const subsectionDescription = $createParagraphNode();
    subsectionDescription.append($createTextNode(description));

    const list = $createListNode('number');
    keyHighlights.forEach((highlight) => {
      const listItem = $createListItemNode();
      const paragraph = $createParagraphNode();
      paragraph.append($createTextNode(highlight));
      listItem.append(paragraph);
      list.append(listItem);
    });

    nodes.push(subsectionHeading, subsectionDescription, list);
  });

  const conclusionParagraph = $createParagraphNode();
  conclusionParagraph.append($createTextNode(breakdown.conclusion));
  nodes.push(conclusionParagraph);

  return nodes;
};
