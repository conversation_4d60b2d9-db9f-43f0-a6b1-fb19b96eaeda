import type { ObjectId } from 'bson';
import type { BackgroundJobPlain, JobType, Task, TaskType } from '../../models/backgroundJob';
import type { HydratedDocument } from 'mongoose';
import type { ReportDocumentStatus, ReportDocumentType } from '../../models/reportDocument';
import type { SerializedEditorState } from 'lexical';
import type { UtrvData } from '../reporting/types';
import type { DynamicSection, OutlineSection } from '../reporting/sections/utils';

type ReportOutlineTaskData = {
  reportType: ReportDocumentType;
  reportId: ObjectId;
};

type XBRLTagSection = {
  title: string;
  description: string;
  keyHighlights: string[];
}

export type SectionBreakdown = {
  introduction: string;
  summary: string;
  xbrlTagSections: XBRLTagSection[];
  conclusion: string;
}

type ProcessSectionTaskData = ReportOutlineTaskData & {
  sectionKey: string;
  section: DynamicSection;
  relevantUtrvData: UtrvData[];
  sectionBreakdown?: SectionBreakdown;
};

type ReportLexicalStateTaskData = ReportOutlineTaskData & {
  sections: Record<string, OutlineSection>;
  lexicalState?: SerializedEditorState;
};

export type SetupIXBRLReportTask = Task<ReportOutlineTaskData, TaskType.SetupIXBRLReport>;
export type ProcessIXBRLReportSectionTask = Task<ProcessSectionTaskData, TaskType.ProcessIXBRLReportSection>;
export type GenerateReportLexicalStateTask = Task<ReportLexicalStateTaskData, TaskType.GenerateReportLexicalState>;

export type AIReportDocumentTask =
  | SetupIXBRLReportTask
  | ProcessIXBRLReportSectionTask
  | GenerateReportLexicalStateTask;

export type AIReportDocumentJobPlain = Omit<BackgroundJobPlain<AIReportDocumentTask[]>, 'initiativeId'> & {
  type: JobType.AIReportDocument;
  initiativeId: ObjectId;
};

export type AIReportDocumentJobModel = HydratedDocument<AIReportDocumentJobPlain>;

export type InitializeLexicalState = {
  status: ReportDocumentStatus;
  lexicalState?: SerializedEditorState;
};
