import ContextError from '../../error/ContextError';
import { JobType, TaskType, type BackgroundJobModel } from '../../models/backgroundJob';
import { ReportDocumentStatus } from '../../models/reportDocument';
import { JobStatus } from '../../models/surveyTemplateHistory';
import { BackgroundBaseWorkflow, type TaskResult } from '../background-process/BackgroundBaseWorkflow';
import { wwgLogger, type LoggerInterface } from '../wwgLogger';
import { getAIReportDocumentService } from './AIReportDocumentService';
import { getReportDocumentManager, type ReportDocumentManager } from './ReportDocumentManager';
import type { AIReportDocumentJobModel, AIReportDocumentTask } from './types';

export class AIReportDocumentWorkflow extends BackgroundBaseWorkflow<AIReportDocumentJobModel> {
  protected jobType = JobType.AIReportDocument;

  constructor(
    protected logger: LoggerInterface,
    private reportDocumentManager: ReportDocumentManager,
    private aiReportDocumentService: ReturnType<typeof getAIReportDocumentService>
  ) {
    super();
  }

  public isAIReportDocumentJob(job: BackgroundJobModel): job is AIReportDocumentJobModel {
    return job.type === this.jobType;
  }

  public async processTask(
    job: AIReportDocumentJobModel,
    task: AIReportDocumentTask
  ): Promise<TaskResult<AIReportDocumentJobModel>> {
    await this.startTask(job, task);
    try {
      switch (task.type) {
        case TaskType.SetupIXBRLReport: {
          await this.aiReportDocumentService.processSetupTask(job, task);
          return { job: await this.completeTask(job, task), executeNextTask: true };
        }
        case TaskType.ProcessIXBRLReportSection: {
          await this.aiReportDocumentService.processIXBRLReportSectionTask(job, task);
          return { job: await this.completeTask(job, task), executeNextTask: true };
        }
        case TaskType.GenerateReportLexicalState: {
          await this.aiReportDocumentService.processReportLexicalStateTask(job, task);
          await this.reportDocumentManager.updateReportStatus({
            reportId: task.data.reportId,
            status: ReportDocumentStatus.Generated,
          });
          return { job: await this.completeTask(job, task), executeNextTask: true };
        }
        default:
          return {
            job: await this.failTask(
              job,
              task,
              new ContextError(`Found not handled job ${job._id} task type ${job.type}`, {
                jobId: job._id,
              })
            ),
            executeNextTask: true,
          };
      }
    } catch (error) {
      job.status = JobStatus.Error;
      await this.reportDocumentManager.updateReportStatus({
        reportId: task.data.reportId,
        status: ReportDocumentStatus.Generated,
      });
      return { job: await this.failTask(job, task, error as Error), executeNextTask: true };
    }
  }
}

let instance: AIReportDocumentWorkflow;
export const getAIReportDocumentWorkflow = () => {
  if (!instance) {
    instance = new AIReportDocumentWorkflow(
      wwgLogger,
      getReportDocumentManager(),
      getAIReportDocumentService()
    );
  }
  return instance;
};
