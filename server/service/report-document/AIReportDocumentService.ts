import { z } from 'zod';
import type { ReportDocumentPlain, ReportDocumentType } from '../../models/reportDocument';
import { getUnifiedAIModelFactory, type UnifiedModelName } from '../ai/UnifiedAIModelFactory';
import { wwgLogger, type LoggerInterface } from '../wwgLogger';
import { REPORT_OUTLINE_PROMPT } from './prompts';
import type { AIReportDocumentJobModel, GenerateReportLexicalStateTask, ProcessIXBRLReportSectionTask, SectionBreakdown, SetupIXBRLReportTask } from './types';
import { isDynamicSection, type DynamicSection } from '../reporting/sections/utils';
import type { UtrvData, XBRLMapping } from '../reporting/types';
import { ObjectId } from 'bson';
import BackgroundJob, { JobType, TaskStatus, TaskType, type CreateJob } from '../../models/backgroundJob';
import { getBackgroundJobService, type BackgroundJobService } from '../background-process/BackgroundJobService';
import { getMappingsByType, getReportOutline } from '../reporting/utils';
import UtrExternalMapping from '../../models/utrExternalMapping';
import { createLogEntry, finalStatuses } from '../jobs';
import { generatedUUID } from '../crypto/token';
import ContextError from '../../error/ContextError';
import { excludeSoftDeleted } from '../../repository/aggregations';
import { universalTrackerLookup } from '../../repository/utrvAggregations';
import UniversalTrackerValue from '../../models/universalTrackerValue';
import Survey, { type SurveyModelPlain } from '../../models/survey';
import { buildAIBreakdownLexicalState } from '../reporting/sections/aiBreakdownBuilder';

const sectionBreakdownSchema = z.object({
  introduction: z.string(),
  summary: z.string(),
  xbrlTagSections: z.array(
    z.object({
      title: z.string(),
      description: z.string(),
      keyHighlights: z.array(z.string()),
    })
  ),
  conclusion: z.string(),
});

class AIReportDocumentService {
  constructor(
    private logger: LoggerInterface,
    private backgroundJobModel: typeof BackgroundJob,
    private universalTrackerValueModel: typeof UniversalTrackerValue,
    private surveyModel: typeof Survey,
    private unifiedModelFactory: ReturnType<typeof getUnifiedAIModelFactory>,
    private bgJobService: BackgroundJobService
  ) {}

  public getIdempotencyKey(context: { initiativeId: ObjectId; reportId: ObjectId }) {
    return `initiativeId:${context.initiativeId}-reportId:${context.reportId}`;
  }

  private findProcessingJob({ initiativeId, reportId }: { initiativeId: ObjectId; reportId: ObjectId }) {
    return this.backgroundJobModel
      .findOne({
        idempotencyKey: this.getIdempotencyKey({ initiativeId, reportId }),
        initiativeId: new ObjectId(initiativeId),
        type: JobType.AIReportDocument,
        status: { $nin: finalStatuses },
      })
      .lean<AIReportDocumentJobModel>()
      .exec();
  }

  public async upsertJob(data: ReportDocumentPlain): Promise<AIReportDocumentJobModel> {
    const { initiativeId, createdBy, type: reportType, _id: reportId } = data;

    const jobType = JobType.AIReportDocument;
    const existingJob = await this.findProcessingJob({ initiativeId, reportId });
    if (existingJob) {
      return existingJob;
    }

    this.logger.info(`Creating ${jobType} job for initiativeId: ${initiativeId}`);

    const setupTask: SetupIXBRLReportTask = {
      id: generatedUUID(),
      name: `Setup ixbrl ${reportType} report`,
      type: TaskType.SetupIXBRLReport,
      status: TaskStatus.Pending,
      data: {
        reportType,
        reportId,
      },
    };

    const createData: CreateJob = {
      idempotencyKey: this.getIdempotencyKey({ initiativeId, reportId }),
      type: jobType,
      name: 'Generating Report Document',
      initiativeId,
      userId: createdBy,
      tasks: [setupTask],
      logs: [createLogEntry(`${jobType} job created for initiative ${initiativeId}`)],
    };

    const job = (await this.backgroundJobModel.create(createData)) as unknown as AIReportDocumentJobModel;
    this.bgJobService.runFromJob(job).catch((e) => {
      this.logger.error(
        new ContextError(`Failed to complete background job type ${job.type}`, {
          debugMessage: `Job "${job.name}" failed to complete`,
          jobId: job._id,
          jobType: job.type,
          cause: e,
        })
      );
    });

    return job;
  }

  public async getUtrvData({ visibleUtrvs, utrCodes }: { visibleUtrvs: ObjectId[]; utrCodes: string[] }) {
    const aggregations = [
      {
        $match: {
          _id: { $in: visibleUtrvs },
          ...excludeSoftDeleted(),
        },
      },
      universalTrackerLookup,
      {
        $match: {
          'universalTracker.code': { $in: utrCodes },
        },
      },
      {
        $project: {
          _id: 1,
          value: 1,
          valueData: 1,
          status: 1,
          effectiveDate: 1,
          universalTracker: {
            $arrayElemAt: ['$universalTracker', 0],
          },
        },
      },
    ];
    return this.universalTrackerValueModel.aggregate<UtrvData>(aggregations).exec();
  }

  public async processSetupTask(job: AIReportDocumentJobModel, task: SetupIXBRLReportTask) {
    /**
     * @todo: Currently hardcoded to use latest survey, should take a list of surveyIds from request
     */
    const survey = await this.surveyModel.findOne({ initiativeId: job.initiativeId, deletedDate: { $exists: false } })
      .populate('initiative')
      .sort({ effectiveDate: -1, created: -1 })
      .lean<SurveyModelPlain>()
      .exec();

    // Figure out which data to use
    const externalMapping = await UtrExternalMapping.find({ type: task.data.reportType }).lean();
    const externalXbrlMapping = externalMapping.reduce((acc, item) => {
      if (!acc[item.mappingCode]) {
        acc[item.mappingCode] = {
          factName: item.mappingCode,
          utrCode: item.utrs[0].utrCode,
          valueListCode: item.utrs[0].valueListCode,
        };
      }
      return acc;
    }, {} as XBRLMapping);

    // Merge default mapping with external mapping
    const mapping = getMappingsByType({ type: task.data.reportType, overrides: externalXbrlMapping });
    const utrCodes = Object.values(mapping).reduce((acc, item) => {
      if (!item) {
        return acc;
      }
      acc.push(item.utrCode);
      return acc;
    }, [] as string[]);

    const utrvData = await this.getUtrvData({ visibleUtrvs: survey?.visibleUtrvs || [], utrCodes });
    const sections = getReportOutline(task.data.reportType);

    Object.entries(sections).forEach(([sectionKey, section]) => {
      if (!isDynamicSection(section)) {
        return;
      }
      const relevantUtrCodes = Object.values(mapping).reduce((acc, item) => {
        if (!item || !section.relatedTags.includes(item.factName)) {
          return acc;
        }
        acc.push(item.utrCode);
        return acc;
      }, [] as string[]);
      const relevantUtrvData = utrvData.filter((d) => relevantUtrCodes.includes(d.universalTracker.code));
      const processSectionTask: ProcessIXBRLReportSectionTask = {
        id: generatedUUID(),
        name: `Process section ${section.heading} of ixbrl ${task.data.reportType} report`,
        type: TaskType.ProcessIXBRLReportSection,
        status: TaskStatus.Pending,
        data: {
          reportId: task.data.reportId,
          reportType: task.data.reportType,
          sectionKey,
          section,
          relevantUtrvData,
        },
      };
      job.tasks.push(processSectionTask);
    });

    const generateLexicalStateTask: GenerateReportLexicalStateTask = {
      id: generatedUUID(),
      name: `Generate lexical state of ixbrl ${task.data.reportType} report`,
      type: TaskType.GenerateReportLexicalState,
      status: TaskStatus.Pending,
      data: {
        reportId: task.data.reportId,
        reportType: task.data.reportType,
        sections,
      },
    };
    job.tasks.push(generateLexicalStateTask);
  }

  public async generateReportSection({
    reportId,
    reportType,
    section,
    relevantUtrvData,
    modelName,
  }: {
    reportId: ObjectId;
    reportType: ReportDocumentType;
    section: DynamicSection;
    relevantUtrvData: UtrvData[];
    modelName: UnifiedModelName;
  }): Promise<SectionBreakdown | undefined> {
    this.logger.info(`Generating report section using model ${modelName}`, {
      reportId,
      reportType,
      modelName,
    });
    const aiModel = this.unifiedModelFactory.getFileSupportModel(modelName);

    const response = await aiModel.executeWithFiles<typeof sectionBreakdownSchema>({
      systemPrompt: REPORT_OUTLINE_PROMPT[reportType].systemPrompt,
      prompt: REPORT_OUTLINE_PROMPT[reportType].userPrompt({ reportOutline: section, sectionData: relevantUtrvData }),
      files: [],
      jsonSchema: sectionBreakdownSchema,
      modelName,
    });

    this.logger.info('Report outline generated', {
      reportId,
      reportType,
      modelName,
      usage: response.tokenUsage,
    });

    return response.data;
  }

  public async processIXBRLReportSectionTask(job: AIReportDocumentJobModel, task: ProcessIXBRLReportSectionTask) {
    const { reportId, reportType, section, relevantUtrvData } = task.data;
    task.data.sectionBreakdown = await this.generateReportSection({
      reportId,
      reportType,
      section,
      relevantUtrvData,
      // @todo: make this configurable
      // "claude-sonnet-4-20250514" | "claude-opus-4-20250514" | "gemini-2.5-pro" | "gemini-2.5-flash"
      modelName: 'gemini-2.5-flash',
    });
  }

  public async processReportLexicalStateTask(job: AIReportDocumentJobModel, task: GenerateReportLexicalStateTask) {
    const { reportId, reportType, sections } = task.data;
    const sectionsData = job.tasks.reduce((acc, task) => {
      if (task.type !== TaskType.ProcessIXBRLReportSection || !task.data.sectionBreakdown) {
        return acc;
      }
      acc[task.data.sectionKey] = {
        relevantUtrvData: task.data.relevantUtrvData,
        breakdown: task.data.sectionBreakdown,
      };
      return acc;
    }, {} as Record<string, { relevantUtrvData: UtrvData[]; breakdown: SectionBreakdown }>);

    this.logger.info('Generating report lexical state', {
      reportId,
      reportType,
    });
    task.data.lexicalState = await buildAIBreakdownLexicalState({ reportId, reportType, sections, sectionsData });
  }
}

let instance: AIReportDocumentService | undefined;
export const getAIReportDocumentService = () => {
  if (!instance) {
    instance = new AIReportDocumentService(
      wwgLogger,
      BackgroundJob,
      UniversalTrackerValue,
      Survey,
      getUnifiedAIModelFactory(),
      getBackgroundJobService()
    );
  }
  return instance;
};
