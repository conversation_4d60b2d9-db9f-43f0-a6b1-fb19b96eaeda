/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { generateRandomToken } from '../service/crypto/token';

export const toCodeFormat = (code: string) => {
  return code.toLowerCase().replace(/[^a-z0-9\/]/gi, '-');
}

export const createReportingCompanyCode = (initiativeName: string) => {
  return toCodeFormat(`organization/${initiativeName}/${generateRandomToken(8)}`);
};

export const createSurveyCode = (initiativeCode: string) => {
  return toCodeFormat(`survey/${initiativeCode}/${generateRandomToken(8)}`);
};

export const truncate = (text: string, n: number, useWordBoundary: boolean = false): string => {
  if (text.length <= n) {
    return text;
  }
  const subString = text.substr(0, n-1); // the original check
  return (useWordBoundary
    ? subString.substr(0, subString.lastIndexOf(" "))
    : subString) + "...";
};


export type SnakeToCamel<S extends string> = S extends `${infer Start}_${infer Rest}` ? `${Start}${Capitalize<SnakeToCamel<Rest>>}` : S

export function capitalize<S extends string>(string: S): Capitalize<S> {
  if (string.length === 0) {
    return "" as never
  }

  return (string[0].toUpperCase() + string.slice(1)) as never
}


export const camelCaseToWords = (text: string) => {
  const result = text.replace(/([A-Z])/g, " $1");
  return result.charAt(0).toUpperCase() + result.slice(1);
}

export function snakeToCamel<S extends string>(string: S): SnakeToCamel<S> {
  const [start, ...rest] = string.split("_")

  return (start + rest.map(capitalize).join('')) as never
}

export const snakeToWords = (text: string) => {
  return text.split('_').map(capitalize).join(' ')
}

/** If true, the input is expected to be parsable to number */
export const isNumericString = (s: unknown) => {
  if (typeof s === 'number') {
    return true;
  }

  if (typeof s !== 'string' || !s) {
    return false; // Empty string skips
  }
  return !Number.isNaN(Number(s));
};


// eslint-disable-next-line @typescript-eslint/unbound-method
export const naturalSort = (new Intl.Collator(undefined, { numeric: true, sensitivity: 'base' })).compare;

export const getSearchRegex = (searchStr: string) => {
  const search = decodeURIComponent(searchStr).replace(/ /g, '|');
  return new RegExp('.*(' + search + ').*', 'i')
}

export const getBreadcrumbs = (input: string[]) => {
  return input.filter((str) => str !== undefined && str !== '').join(' / ') || '';
};

export const extractVersion = (releaseName: string) => {
  return releaseName.match(/\d+\.\d+\.\d+/)?.[0];
};

export const normalizeText = (input: string | number | undefined, options: { lowercase?: boolean } = {}) => {
  if (input === undefined || typeof input === 'number') {
    return input;
  }
  const result = input.trim().replace(/\s{2,}/g, ' ');
  return options.lowercase ? result.toLowerCase() : result;
};
