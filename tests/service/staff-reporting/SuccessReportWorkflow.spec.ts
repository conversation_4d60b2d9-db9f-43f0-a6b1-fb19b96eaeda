import { ObjectId } from 'bson';
import { expect } from 'chai';
import mongoose from 'mongoose';
import sinon from 'sinon';
import ContextError from '../../../server/error/ContextError';
import BackgroundJob, { activeJobStatuses, JobStatus, JobType, TaskStatus, TaskType } from '../../../server/models/backgroundJob';
import { getBackgroundJobService } from '../../../server/service/background-process/BackgroundJobService';
import { generatedUUID } from '../../../server/service/crypto/token';
import { SupportedJobModel } from '../../../server/service/pptx-report/types';
import { getSuccessReportManager } from '../../../server/service/staff-reporting/SuccessReportManager';
import {
  GenerateSuccessReportTask,
  SuccessReportWorkflow,
  SurveyEngagementJob,
} from '../../../server/service/staff-reporting/SuccessReportWorkflow';
import { getWorkflowFileStorage } from '../../../server/service/survey/workflow/WorkflowFileStorage';
import { wwgLogger } from '../../../server/service/wwgLogger';
import { getCurrentDateStr } from '../../../server/util/date';
import setup from '../../setup';


describe('SuccessReportWorkflow', () => {
  const sandbox = sinon.createSandbox();
  const workflowFileStorage = getWorkflowFileStorage();
  const bgJobService = getBackgroundJobService();
  const successReportManager = getSuccessReportManager();

  const workflow = new SuccessReportWorkflow(wwgLogger, JobType.SuccessReport, bgJobService, successReportManager);

  afterEach(() => {
    sandbox.restore();
  });

  describe('create', () => {
    it('should throw error if active job exists within 24 hours', async () => {
      sandbox.stub(BackgroundJob, 'exists').resolves({ _id: new ObjectId() });
      await expect(workflow.create()).to.eventually.be.rejectedWith(
        /Survey engagement export job already exists for this configuration/
      );
    });

    it('should check for active jobs with correct parameters', async () => {
      const existsStub = sandbox.stub(BackgroundJob, 'exists').resolves(null);
      const backgroundJob = new BackgroundJob({
        _id: new ObjectId(),
        name: `${getCurrentDateStr()} ${TaskType.GenerateSurveyEngagementReport}`,
        type: JobType.SuccessReport,
        status: JobStatus.Pending,
        tasks: [],
      }) as unknown as SupportedJobModel;
      setup.wrapStub(sandbox, BackgroundJob, 'create', () => backgroundJob);

      await workflow.create();

      // Verify the exists query uses correct parameters
      const queryParams = existsStub.firstCall.args[0] as any;
      expect(queryParams.type).to.equal(JobType.SuccessReport);
      expect(queryParams.status).to.deep.equal({ $in: activeJobStatuses });
      expect(queryParams.created).to.exist;
      expect(queryParams.created.$gte).to.be.instanceOf(Date);

      // Verify the date is approximately 24 hours ago (within a minute tolerance)
      const twentyFourHoursAgo = new Date(Date.now() - 1000 * 60 * 60 * 24);
      const timeDiff = Math.abs(queryParams.created.$gte.getTime() - twentyFourHoursAgo.getTime());
      expect(timeDiff).to.be.lessThan(60000); // Within 1 minute
    });

    it('should create if job does not exist', async () => {
      const backgroundJob = new BackgroundJob({
        _id: new ObjectId(),
        name: `${getCurrentDateStr()} ${TaskType.GenerateSurveyEngagementReport}`,
        type: JobType.SuccessReport,
        status: JobStatus.Pending,
        tasks: [],
      }) as unknown as SupportedJobModel;
      sandbox.stub(BackgroundJob, 'exists').resolves(null);
      const createStub = setup.wrapStub(sandbox, BackgroundJob, 'create', () => backgroundJob);

      const result = await workflow.create();
      const createData = createStub.args[0][0];

      expect(createData.tasks.length).to.equal(1);
      expect(createData.tasks[0].name).to.equal('Generate survey engagement report');
      expect(createData.tasks[0].type).to.equal(TaskType.GenerateSurveyEngagementReport);
      expect(createData.tasks[0].status).to.equal(TaskStatus.Pending);

      expect(result.jobId instanceof mongoose.Types.ObjectId).to.be.true;
      expect(result.taskId).to.equal(createData.tasks[0].id);
    });
  });

  describe('processTask', () => {
    it('should throw error if task type is not supported', async () => {
      await expect(workflow.processTask({} as any, { type: 'Unsupported Task' } as any)).to.eventually.be.rejectedWith(
        ContextError,
        /Found not handled job/
      );
    });
    it('should process task if task type is supported', async () => {
      const task: GenerateSuccessReportTask = {
        id: generatedUUID(),
        name: 'Generate survey engagement report',
        type: TaskType.GenerateSurveyEngagementReport,
        status: TaskStatus.Pending,
        data: {},
      };
      const backgroundJob = new BackgroundJob({
        _id: new ObjectId(),
        name: `${getCurrentDateStr()} ${TaskType.GenerateSurveyEngagementReport}`,
        type: JobType.SuccessReport,
        status: JobStatus.Pending,
        tasks: [task],
      }) as SurveyEngagementJob;

      sandbox.stub(workflowFileStorage, 'streamFileUpload').resolves({ path: 'filePath' });
      const generateSurveyEngagementStub = sandbox
        .stub(successReportManager, 'getReport')
        .resolves({ data: [], header: [] });

      const result = await workflow.processTask(backgroundJob, task);
      expect(result.job.tasks.length).to.equal(2);
      expect(generateSurveyEngagementStub.calledOnce).to.be.true;

      const [surveyEngagementTask, companyUpgradesTask] = result.job.tasks; // after processTask() => next task is added

      expect(surveyEngagementTask.type).to.equal(TaskType.GenerateSurveyEngagementReport);
      expect(surveyEngagementTask.data.filePath).to.equal('filePath');
      expect(companyUpgradesTask.type).to.equal(TaskType.GenerateCompanyUpgradesReport);
    });
    it('should process task if task type is supported', async () => {
      const task: GenerateSuccessReportTask = {
        id: generatedUUID(),
        name: 'Generate company upgrades report',
        type: TaskType.GenerateCompanyUpgradesReport,
        status: TaskStatus.Pending,
        data: {},
      };
      const backgroundJob = new BackgroundJob({
        _id: new ObjectId(),
        name: `${getCurrentDateStr()} ${TaskType.GenerateSurveyEngagementReport}`,
        type: JobType.SuccessReport,
        status: JobStatus.Pending,
        tasks: [task],
      }) as SurveyEngagementJob;

      sandbox.stub(workflowFileStorage, 'streamFileUpload').resolves({ path: 'filePath' });
      const generateCompanyUpgradesStub = sandbox
      .stub(successReportManager, 'getReport')
      .resolves({ data: [], header: [] });

      const result = await workflow.processTask(backgroundJob, task);
      expect(generateCompanyUpgradesStub.calledOnce).to.be.true;

      const [companyUpgradesTask] = result.job.tasks;

      expect(companyUpgradesTask.type).to.equal(TaskType.GenerateCompanyUpgradesReport);
      expect(companyUpgradesTask.data.filePath).to.equal('filePath');
    });
  });
});
