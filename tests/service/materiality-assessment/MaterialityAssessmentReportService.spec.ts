import { createMongooseModel } from '../../setup';
import { utils } from '@sheet/core';
import { ObjectId } from 'bson';
import { expect } from 'chai';
import sinon from 'sinon';
import UserError from '../../../server/error/UserError';
import { JobStatus } from '../../../server/models/backgroundJob';
import { MaterialityAssessmentRepository } from '../../../server/repository/MaterialityAssessmentRepository';
import { BackgroundJobService } from '../../../server/service/background-process/BackgroundJobService';
import { getExcel } from '../../../server/service/file/Excel';
import { MaterialityAssessmentBackgroundJobService } from '../../../server/service/materiality-assessment/MaterialityAssessmentBackgroundJobService';
import {
  MaterialityAssessmentReportService,
  getMaterialityAssessmentReportService,
} from '../../../server/service/materiality-assessment/MaterialityAssessmentReportService';
import { MaterialityAssessmentService } from '../../../server/service/materiality-assessment/MaterialityAssessmentService';
import { AssessmentResultType } from '../../../server/service/materiality-assessment/types';
import User from '../../../server/models/user';

const mockAssessment = {
  _id: new ObjectId(),
  name: 'Assessment 1',
  effectiveDate: new Date(),
  visibleUtrvs: [],
};

describe('MaterialityAssessmentReportService', () => {
  let backgroundJobServiceStub: sinon.SinonStubbedInstance<BackgroundJobService>;
  let materialityAssessmentRepositoryStub: sinon.SinonStubbedInstance<MaterialityAssessmentRepository>;
  let materialityAssessmentServiceGetResultStub: sinon.SinonStub;
  let backgroundJobServiceFindJobStub: sinon.SinonStub;
  let userFindStub: sinon.SinonStub;

  let service: MaterialityAssessmentReportService;

  beforeEach(() => {
    backgroundJobServiceStub = sinon.createStubInstance(BackgroundJobService);
    materialityAssessmentRepositoryStub = sinon.createStubInstance(MaterialityAssessmentRepository);

    materialityAssessmentServiceGetResultStub = sinon.stub(
      MaterialityAssessmentService.prototype,
      'getResult'
    );

    // Mock the instance and method of MaterialityAssessmentBackgroundJobService
    backgroundJobServiceFindJobStub = sinon.stub(
      MaterialityAssessmentBackgroundJobService.prototype,
      'findExistingJob'
    );

    // Mock User.find to return empty array by default
    userFindStub = sinon.stub(User, 'find').returns(createMongooseModel([]));

    service = new MaterialityAssessmentReportService(
      getExcel(),
      backgroundJobServiceStub,
      materialityAssessmentRepositoryStub
    );
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('downloadAssessmentResults', () => {
    it('should throw an error if the job is not available or in pending status', async () => {
      backgroundJobServiceFindJobStub.resolves({ status: JobStatus.Pending });

      await expect(
        service.downloadAssessmentResults({
          initiativeId: new ObjectId(),
          surveyId: new ObjectId(),
          type: AssessmentResultType.Financial,
        })
      ).to.be.rejectedWith(UserError, /The report is not ready to generate yet/);
    });

    it('should create a workbook with assessment results', async () => {
      const job = {
        status: JobStatus.Completed,
        tasks: [{ data: {} }],
      };
      backgroundJobServiceFindJobStub.resolves(job);
      materialityAssessmentRepositoryStub.getAssessmentData.resolves([mockAssessment]);
      materialityAssessmentServiceGetResultStub.resolves({
        financial: [],
        nonFinancial: [],
        debug: {
          financial: {},
          nonFinancial: {},
        },
      });
      const result = await service.downloadAssessmentResults({
        initiativeId: new ObjectId(),
        surveyId: new ObjectId(),
        type: AssessmentResultType.Financial,
      });

      expect(result).to.have.property('workbook');
      expect(result).to.have.property('filename');
      expect(result.workbook.SheetNames).to.deep.eq(['Financial', 'Metrics', 'Financial Scores']);
      expect(utils.sheet_to_json(result.workbook.Sheets['Metrics'])).to.deep.eq([]);
    });

    it('should format and sort results correctly', async () => {
      const job = {
        status: JobStatus.Completed,
        tasks: [
          {
            data: {
              result: {
                financial: [
                  {
                    name: 'A',
                    code: 'A',
                    score: 2,
                    categories: { esg: [], sdg: ['S1', 'S2'], materialPillar: [], boundary: [] },
                    utrMapping: [{ code: 'U2' }, { code: 'U3' }],
                  },
                  {
                    name: 'B',
                    code: 'B',
                    score: 3,
                    categories: { esg: ['E1'], sdg: [], materialPillar: [], boundary: [] },
                    utrMapping: [{ code: 'U1' }],
                  },
                ],
              },
            },
          },
        ],
      };
      backgroundJobServiceFindJobStub.resolves(job);
      materialityAssessmentRepositoryStub.getAssessmentData.resolves([mockAssessment]);

      const { workbook } = await service.downloadAssessmentResults({
        initiativeId: new ObjectId(),
        surveyId: new ObjectId(),
        type: AssessmentResultType.Financial,
      });
      const sheet = workbook.Sheets['Financial'];
      const data = utils.sheet_to_json(sheet);

      expect(data).to.deep.eq([
        {
          'Material topic': 'B',
          Materiality: 3,
          'Mapped UTR codes': 'U1',
          ESGs: 'E1',
          SDGs: '',
          'Material pillars': '',
          'Material boundaries': '',
        },
        {
          'Material topic': 'A',
          Materiality: 2,
          'Mapped UTR codes': 'U2, U3',
          ESGs: '',
          SDGs: 'S1, S2',
          'Material pillars': '',
          'Material boundaries': '',
        },
      ]);
    });

    it('should format assessment answers correctly', async () => {
      const job = {
        status: JobStatus.Completed,
        tasks: [{ data: { result: { financial: [], nonFinancial: [] } } }],
      };
      backgroundJobServiceFindJobStub.resolves(job);
      materialityAssessmentRepositoryStub.getAssessmentData.resolves([
        {
          ...mockAssessment,
          visibleUtrvs: [
            {
              _id: new ObjectId(),
              status: 'verified',
              universalTracker: { _id: new ObjectId(), name: 'Metric 1', code: 'M1' },
              valueData: { data: 'ValueListCode1' },
              valueList: [{ code: 'ValueListCode1', name: 'ValueList Name 1\n\t\r' }],
            },
            {
              _id: new ObjectId(),
              status: 'created',
              universalTracker: { _id: new ObjectId(), name: '\n\t\rMetric 2\n\t\r', code: 'M2' },
              valueData: {},
              valueList: [],
            },
            {
              _id: new ObjectId(),
              status: 'verified',
              universalTracker: { _id: new ObjectId(), name: '\n\t\rMetric 3', code: 'M3' },
              valueData: { data: ['ValueListCode3', 'ValueListCode4'] },
              valueList: [
                { code: 'ValueListCode3', name: '\n\t\rValueList Name 3' },
                { code: 'ValueListCode4', name: 'ValueList\n\t Name 4\n\t\r' },
              ],
            },
          ],
        },
      ]);

      const result = await service.downloadAssessmentResults({
        initiativeId: new ObjectId(),
        surveyId: new ObjectId(),
        type: AssessmentResultType.Financial,
      });

      const sheet = result.workbook.Sheets['Metrics'];
      const data = utils.sheet_to_json(sheet);
      expect(data).to.deep.eq([
        { 'Metric Title': 'Metric 1', 'Metric Code': 'M1', Answer: 'ValueList Name 1', 'Latest Contributor': '', 'Latest Verifier': '' },
        { 'Metric Title': 'Metric 2', 'Metric Code': 'M2', Answer: '', 'Latest Contributor': '', 'Latest Verifier': '' },
        { 'Metric Title': 'Metric 3', 'Metric Code': 'M3', Answer: 'ValueList Name 3, ValueList Name 4', 'Latest Contributor': '', 'Latest Verifier': '' },
      ]);
    });
  });

  describe('downloadAssessmentAnswers', () => {
    it('should create a workbook with assessment answers', async () => {
      materialityAssessmentRepositoryStub.getAssessmentData.resolves([
        {
          ...mockAssessment,
          visibleUtrvs: [
            {
              _id: new ObjectId(),
              status: 'verified',
              universalTracker: { _id: new ObjectId(), name: 'Metric 1', code: 'M1' },
              valueData: { data: 'ValueListCode1' },
              valueList: [{ code: 'ValueListCode1', name: 'ValueList Name 1' }],
            },
            {
              _id: new ObjectId(),
              status: 'created',
              universalTracker: { _id: new ObjectId(), name: 'Metric 2', code: 'M2' },
              valueData: undefined,
              valueList: [],
            },
          ],
        },
      ]);

      const result = await service.downloadAssessmentAnswers({
        surveyId: new ObjectId(),
      });

      expect(result).to.have.property('workbook');
      expect(result).to.have.property('filename');
      expect(result.workbook.SheetNames).to.deep.eq(['Metrics']);
      const sheet = result.workbook.Sheets['Metrics'];
      const data = utils.sheet_to_json(sheet);
      expect(data).to.deep.eq([
        { 'Metric Title': 'Metric 1', 'Metric Code': 'M1', Answer: 'ValueList Name 1', 'Latest Contributor': '', 'Latest Verifier': '' },
        { 'Metric Title': 'Metric 2', 'Metric Code': 'M2', Answer: '', 'Latest Contributor': '', 'Latest Verifier': '' },
      ]);
    });

    it('should handle cases with special characters in metric names and answers', async () => {
      materialityAssessmentRepositoryStub.getAssessmentData.resolves([
        {
          ...mockAssessment,
          visibleUtrvs: [
            {
              _id: new ObjectId(),
              status: 'verified',
              universalTracker: { _id: new ObjectId(), name: 'Metric with\nnewline', code: 'M1' },
              valueData: { data: 'ValueListCode1' },
              valueList: [{ code: 'ValueListCode1', name: 'Answer with\t tab' }],
            },
            {
              _id: new ObjectId(),
              status: 'verified',
              universalTracker: { _id: new ObjectId(), name: '\rMetric with carriage return', code: 'M3' },
              valueData: { data: ['ValueListCode3', 'ValueListCode4'] },
              valueList: [
                { code: 'ValueListCode3', name: '\nValueList Name 3' },
                { code: 'ValueListCode4', name: 'ValueList\n Name 4\n\t' },
              ],
            },
          ],
        },
      ]);

      const result = await service.downloadAssessmentAnswers({
        surveyId: new ObjectId(),
      });

      const sheet = result.workbook.Sheets['Metrics'];
      const data = utils.sheet_to_json(sheet);
      expect(data).to.deep.eq([
        { 'Metric Title': 'Metric withnewline', 'Metric Code': 'M1', Answer: 'Answer with tab', 'Latest Contributor': '', 'Latest Verifier': '' },
        { 'Metric Title': 'Metric with carriage return', 'Metric Code': 'M3', Answer: 'ValueList Name 3, ValueList Name 4', 'Latest Contributor': '', 'Latest Verifier': '' },
      ]);
    });

    it('should return an empty metrics sheet if assessment data has no visibleUtrvs', async () => {
      materialityAssessmentRepositoryStub.getAssessmentData.resolves([{ ...mockAssessment, visibleUtrvs: [] }]);

      const result = await service.downloadAssessmentAnswers({
        surveyId: new ObjectId(),
      });

      expect(result.workbook.SheetNames).to.deep.eq(['Metrics']);
      const sheet = result.workbook.Sheets['Metrics'];
      const data = utils.sheet_to_json(sheet);
      expect(data).to.deep.eq([]);
    });

    it('should include contributor and verifier names when available', async () => {
      const contributorId = new ObjectId();
      const verifierId = new ObjectId();

      materialityAssessmentRepositoryStub.getAssessmentData.resolves([
        {
          ...mockAssessment,
          visibleUtrvs: [
            {
              _id: new ObjectId(),
              status: 'verified',
              universalTracker: { _id: new ObjectId(), name: 'Metric 1', code: 'M1' },
              valueData: { data: 'ValueListCode1' },
              valueList: [{ code: 'ValueListCode1', name: 'ValueList Name 1' }],
              latestContributorId: contributorId,
              latestVerifierId: verifierId,
            },
            {
              _id: new ObjectId(),
              status: 'created',
              universalTracker: { _id: new ObjectId(), name: 'Metric 2', code: 'M2' },
              valueData: { data: 'ValueListCode2' },
              valueList: [{ code: 'ValueListCode2', name: 'ValueList Name 2' }],
              latestContributorId: contributorId,
              // No verifier since status is not 'verified'
            },
          ],
        },
      ]);

      // Mock User.find to return test users
      userFindStub.returns(createMongooseModel([
        { _id: contributorId, firstName: 'John', surname: 'Doe' },
        { _id: verifierId, firstName: 'Jane', surname: 'Smith' },
      ]));

      const result = await service.downloadAssessmentAnswers({
        surveyId: new ObjectId(),
      });

      const sheet = result.workbook.Sheets['Metrics'];
      const data = utils.sheet_to_json(sheet);
      expect(data).to.deep.eq([
        {
          'Metric Title': 'Metric 1',
          'Metric Code': 'M1',
          Answer: 'ValueList Name 1',
          'Latest Contributor': 'John Doe',
          'Latest Verifier': 'Jane Smith'
        },
        {
          'Metric Title': 'Metric 2',
          'Metric Code': 'M2',
          Answer: 'ValueList Name 2',
          'Latest Contributor': 'John Doe',
          'Latest Verifier': ''
        },
      ]);
    });
  });

  describe('getMaterialityAssessmentReportService', () => {
    it('should return a singleton instance', () => {
      const instance1 = getMaterialityAssessmentReportService();
      const instance2 = getMaterialityAssessmentReportService();
      expect(instance1).to.equal(instance2);
    });
  });
});
