/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import * as chai from 'chai';
import { expect } from "chai";
import {
  createUtr,
  createUtrFromCode,
  fragmentUtrOne, utrTableOne, utrTableWithColumnTypePercentage,
} from "../../../fixtures/universalTrackerFixtures";
import { AggregatedUniversalTracker } from "../../../../server/service/utr/aggregation/AggregatedUniversalTracker";
import { createUtrv, getUtrv } from "../../../fixtures/compositeUTRVFixtures";
import { ObjectId } from 'bson';
import {
  TableData,
  ValueData,
} from "../../../../server/models/public/universalTrackerValueType";
import { UtrValueType, ValueAggregation, ColumnValueAggregation, TableAggregationType, ColumnType } from '../../../../server/models/public/universalTrackerType';
import { valueListTestTable } from '../../../fixtures/valueListFixtures';
import { UtrvType } from '../../../../server/service/utr/constants';
import { AggregationMode } from '../../../../server/models/public/universalTrackerType';
import { UniversalTrackerPlain } from "../../../../server/models/universalTracker";
import { getUtrvWithUtrId } from "../../../factories/universalTrackerValue";
import { InputData } from "../../../../server/service/pptx-report/templates/PPTXTemplateInterface";

const createUtrvOverrides = (effectiveDate: Date, data: TableData) => ({ effectiveDate, valueData: { table: data } });

const createRow = (...values: (string | number | undefined | InputData)[]) => {
  return values.map((v, i) => {
    const code = `col${i + 1}`;
    return typeof v === 'object' ? { ...v, code } : { value: v, code: code };
  });
};

const getMultiUtr = (valueList = {}) => ({
  ...fragmentUtrOne,
  valueType: UtrValueType.ValueListMulti,
  valueValidation: {
    valueList: {
      type: 'custom' as any,
      custom: [
        { code: 'yes', name: 'Yes' },
        { code: 'no', name: 'No' },
      ],
      ...valueList,
    },
  },
});

const createUtrvs = (valueData: (ValueData | undefined)[], utrId = new ObjectId()) => valueData.map(vd => {
  return createUtrv(new ObjectId(), utrId, undefined, { valueData: vd })
})

type NumericString = number | string | undefined;
describe('AggregatedUniversalTracker model', () => {

  const numericAggCount = (values: (undefined | number)[]) => values.filter(v => v !== undefined);

  describe('type number', function () {
    const utr = createUtr(new ObjectId(), 'random-two', { valueType: UtrValueType.Number });

    [
      {
        values: [20, 30],
        expected: 50,
      },
      {
        values: [0, 2.2, 55],
        expected: 57.2,
      },
      {
        values: [undefined, 2.2, 55],
        expected: 57.2,
      },
      {
        values: [undefined, undefined, undefined],
        expected: undefined,
      },
      {
        values: [5000, 500, 55],
        expected: 5555,
      },
    ].forEach(({ values, expected }) => {

      it(`should add ${values}, expect ${expected}`, function () {
        const result = new AggregatedUniversalTracker(utr);
        values.forEach(v => {
          result.add(createUtrv(new ObjectId(), utr._id, v))
        })
        chai.expect(result.aggregationCount).eqls(numericAggCount(values).length);
        chai.expect(result.value).eq(expected);
      });
    })

  });

  describe('type percentage', function () {

    const valueType = UtrValueType.Percentage;
    const utr = createUtr(new ObjectId(), 'random-two', { valueType });
    utr.valueType = valueType;

    it('should average percentage', function () {
      const result = new AggregatedUniversalTracker(utr);
      chai.expect(result.aggregationCount).eqls(0);
      result.add(createUtrv(new ObjectId(), utr._id, 20))
      result.add(createUtrv(new ObjectId(), utr._id, 30))

      chai.expect(result.aggregationCount).eqls(2);
      chai.expect(result.value).eq(25);
    });

    [
      {
        values: [undefined, 40],
        expected: 40,
      },
      {
        values: [undefined, undefined, undefined],
        expected: undefined,
      },
    ].forEach(({ values, expected }) => {
      it(`should add ${values}, expect ${expected}`, function () {
        const result = new AggregatedUniversalTracker(utr, [], UtrvType.Actual, AggregationMode.Combined);
        values.forEach(v => {
          result.add(createUtrv(new ObjectId(), utr._id, v))
        })
        chai.expect(result.aggregationCount).eqls(numericAggCount(values).length);
        chai.expect(result.value).eq(expected);
      });
    });

    [
      {
        values: [10, 40],
        expected: 50,
      },
      {
        values: [10, 20, 30],
        expected: 60,
      },
    ].forEach(({ values, expected }) => {
      const utr = createUtr(new ObjectId(), 'random-three', { valueType, valueAggregation: ValueAggregation.ValueSumAggregator });
      it(`Percentage can use ValueAggregation.ValueSumAggregator for AggrgationMode.Combined`, function () {
        const result = new AggregatedUniversalTracker(utr, [], UtrvType.Actual, AggregationMode.Combined);
        values.forEach(v => {
          result.add(createUtrv(new ObjectId(), utr._id, v))
        })
        chai.expect(result.aggregationCount).eqls(numericAggCount(values).length);
        chai.expect(result.value).eq(expected);
      });
    });

    [
      {
        values: [10, 40],
        expected: 25, // Don't sum and fall back to default Average
      },
      {
        values: [10, 20, 30],
        expected: 20, // Don't sum and fall back to default Average
      },
    ].forEach(({ values, expected }) => {
      const utr = createUtr(new ObjectId(), 'random-three', { valueType, valueAggregation: ValueAggregation.ValueSumAggregator });
      it(`Percentage can NOT use ValueAggregation.ValueSumAggregator for AggrgationMode.Children`, function () {
        const result = new AggregatedUniversalTracker(utr, [], UtrvType.Actual, AggregationMode.Children);
        values.forEach(v => {
          result.add(createUtrv(new ObjectId(), utr._id, v))
        })
        chai.expect(result.aggregationCount).eqls(numericAggCount(values).length);
        chai.expect(result.value).eq(expected);
      });
    });
  });

  describe(`type ${UtrValueType.Text}`, function () {
    const valueType = UtrValueType.Text;

    it(`${valueType} value - add`, async () => {
      const utr = getMultiUtr();
      utr.valueType = valueType;

      const result = new AggregatedUniversalTracker(utr);
      const data = createUtrvs([{ data: 'yes' }, { data: 'weird' }, { data: 'yesNo' }, { data: 'last' }]);

      data.forEach((d) => result.add(d));
      chai.expect(result.aggregationCount).eqls(data.length);
      chai.expect(result.valueData?.data ?? {}).eqls('last');
    });

    it(`${valueType} value count - add`, async () => {
      const utr = getMultiUtr();
      utr.valueType = valueType;
      utr.valueAggregation = ValueAggregation.TextCountAggregator;

      const result = new AggregatedUniversalTracker(utr, [], UtrvType.Actual, AggregationMode.Combined);
      const data = createUtrvs([{ data: 'yes' }, { data: 'weird' }, { data: 'yesNo' }, { data: 'weird' }]);

      data.forEach((d) => result.add(d));
      chai.expect(result.aggregationCount).eqls(data.length);
      chai.expect(result.valueData?.data ?? {}).eqls({
        yes: 1,
        weird: 2,
        yesNo: 1,
      });
    });

    describe(`${valueType} value concatenate - add`, () => {
      it('all utrvs are answered', () => {
        const utr = createUtr(new ObjectId(), 'text-question', {
          valueType: UtrValueType.Text,
          valueAggregation: ValueAggregation.ValueConcatenateAggregator,
        });
        const result = new AggregatedUniversalTracker(utr, [], UtrvType.Actual, AggregationMode.Combined);
        const data = createUtrvs([{ data: 'text one' }, { data: 'text two' }]);
        data.forEach((d) => result.add(d));
        chai.expect(result.aggregationCount).to.be.eq(data.length);
        chai.expect(result.valueData?.data ?? {}).to.be.eq('text one, text two');
      });

      it('utrvs have special cases', () => {
        const utr = createUtr(new ObjectId(), 'text-question', {
          valueType: UtrValueType.Text,
          valueAggregation: ValueAggregation.ValueConcatenateAggregator,
        });
        const result = new AggregatedUniversalTracker(utr, [], UtrvType.Actual, AggregationMode.Combined);
        const data = createUtrvs([
          { data: '' },
          { data: undefined },
          { data: {} },
          { data: [] },
          { data: 'aa, bb,,, cc' },
        ]);
        data.forEach((d) => result.add(d));
        chai.expect(result.aggregationCount).to.be.eq(data.length);
        chai.expect(result.valueData?.data ?? {}).to.be.eq('aa, bb,,, cc');
      });
    })
  });

  describe(`type ${UtrValueType.Date}, (same as text)`, function () {
    const valueType = UtrValueType.Date;

    it(`${valueType} value - add`, async () => {
      const utr = getMultiUtr();
      utr.valueType = valueType;

      const result = new AggregatedUniversalTracker(utr, [], UtrvType.Actual, AggregationMode.Combined);
      const data = createUtrvs([
        { data: '2010-01-01' },
        { data: '2011-01-11' },
        { data: '2011-01-11' },
        { data: '2011-01-11' },
      ]);

      data.forEach(d => result.add(d))
      chai.expect(result.aggregationCount).eqls(data.length);
      chai.expect(result.valueData?.data ?? {}).eqls('2011-01-11');
    });

    it(`${valueType} value count - add`, async () => {
      const utr = getMultiUtr();
      utr.valueType = valueType;
      utr.valueAggregation = ValueAggregation.TextCountAggregator;

      const result = new AggregatedUniversalTracker(utr, [], UtrvType.Actual, AggregationMode.Combined);
      const data = createUtrvs([
        { data: '2010-01-01' },
        { data: '2011-01-11' },
        { data: '2011-01-11' },
        { data: '2011-01-11' },
      ]);

      data.forEach(d => result.add(d))
      chai.expect(result.aggregationCount).eqls(data.length);
      chai.expect(result.valueData?.data ?? {}).eqls({
        '2010-01-01': 1,
        '2011-01-11': 3,
      });
    });

  });

  describe('valueList', () => {

    it('valueList simple value', async () => {
      const utr = getMultiUtr();
      utr.valueType = UtrValueType.ValueList;

      const result = new AggregatedUniversalTracker(utr);
      chai.expect(result.aggregationCount).eqls(0);
    });

    it('valueListAggregator simple value - add', async () => {
      const utr = getMultiUtr();
      utr.valueType = 'valueList' as any;

      const result = new AggregatedUniversalTracker(utr);
      const data = createUtrvs([{ data: 'first' }, { data: 'second' }])
      data.forEach(d => result.add(d))
      chai.expect(result.aggregationCount).eqls(data.length);
      chai.expect(result.valueData?.data ?? {}).eqls('second');
    });

    it('valueListAggregator simple multi value - add', async () => {
      const utr = getMultiUtr();

      const result = new AggregatedUniversalTracker(utr);
      const data = createUtrvs([{ data: ['first'], }, { data: ['second'] }, { data: ['second', 'third'] }])
      data.forEach(d => result.add(d))
      chai.expect(result.aggregationCount).eqls(data.length);
      chai.expect(result.valueData?.data ?? {}).eqls(['second', 'third']);
    });

    it('valueListCountAggregator simple value - add', async () => {
      const utr = getMultiUtr();
      utr.valueType = 'valueList' as any;
      utr.valueAggregation = ValueAggregation.ValueListCountAggregator;

      const result = new AggregatedUniversalTracker(utr, [], UtrvType.Actual, AggregationMode.Combined);
      const data = createUtrvs([{ data: 'yes' }])
      data.forEach(d => result.add(d))
      chai.expect(result.aggregationCount).eqls(data.length);
      chai.expect(result.valueData?.data ?? {}).eqls({
        yes: 1,
      });
    });

    it('valueListCountAggregator multi value - add', async () => {
      const utr = getMultiUtr();
      utr.valueAggregation = ValueAggregation.ValueListCountAggregator;

      const result = new AggregatedUniversalTracker(utr, [], UtrvType.Actual, AggregationMode.Combined);
      const data = createUtrvs([{ data: ['yes'] }, { data: ['yes', 'no'] }])
      data.forEach(d => result.add(d))
      chai.expect(result.aggregationCount).eqls(data.length);
      chai.expect(result.valueData?.data ?? {}).eqls({ yes: 2, no: 1 });
    });

    it('valueListCountAggregator multi value - empty', async () => {
      const utr = getMultiUtr();
      utr.valueAggregation = ValueAggregation.ValueListCountAggregator;

      const result = new AggregatedUniversalTracker(utr, [], UtrvType.Actual, AggregationMode.Combined);
      const data = createUtrvs([undefined, undefined])
      data.forEach(d => result.add(d))
      chai.expect(result.aggregationCount).eqls(data.length);
      chai.expect(result.valueData?.data ?? {}).eqls({});
    });

  });

  describe(`numericValueList value`, function () {
    const valueType = UtrValueType.NumericValueList;
    const utr = getMultiUtr();
    utr.valueType = valueType;
    const utrValueListCodes = valueListTestTable.options.map(o => o.code);

    const createDataRecord = (...codeValues: NumericString[]) => codeValues.reduce<Record<string, NumericString>>((a, c, i) => {
      const code = utrValueListCodes[i] ?? `unknownCode${i}`;
      a[code] = c;
      return a;
    }, {});

    const createNumericData = (value: number, ...codeValues: NumericString[]) => ({
      value,
      valueData: { data: createDataRecord(...codeValues) },
    });

    const sumTestCases = [
      {
        values: [{ value: undefined }, { value: undefined }],
        expectedData: createDataRecord(),
        expectedValue: undefined,
      },
      {
        values: [createNumericData(60, '10', '20', '30'), createNumericData(30, '10', '15', '5')],
        expectedData: createDataRecord(20, 35, 35),
        expectedValue: 90,
      },
      {
        values: [createNumericData(500, '0', '200', '300'), createNumericData(30, '1', '20', '9')],
        expectedData: createDataRecord(1, 220, 309),
        expectedValue: 530,
      },
      {
        values: [createNumericData(500, '0', '200', '300'), createNumericData(30, '1', '20', '9')],
        expectedData: createDataRecord(1, 220, 309),
        expectedValue: 530,
      },
    ];

    sumTestCases.forEach(({ values, expectedData, expectedValue }) => {
      it(`should add ${valueType}, expect ${Object.values(expectedData)} and value: ${expectedValue}`, function () {
        const result = new AggregatedUniversalTracker(utr);
        values.forEach((v) => {
          result.add(getUtrv(v));
        });
        chai.expect(result.aggregationCount).eqls(values.length);
        chai.expect(result.value).eq(expectedValue);
        chai.expect(result.valueData?.data ?? {}).eqls(expectedData);
      });
    });

    describe(`${valueType} value average - add`, () => {
      const utr = {
        ...getMultiUtr(),
        valueType: UtrValueType.NumericValueList,
        valueAggregation: ValueAggregation.NumericValueListAverageAggregator,
      };
      const averageTestCases = [
        {
          values: [{ value: undefined }, { value: undefined }],
          expectedData: createDataRecord(),
          expectedValue: undefined,
        },
        {
          values: [createNumericData(60, '10', '20', '30'), createNumericData(30, '10', '15', '10')],
          expectedData: createDataRecord(10, 17.5, 20),
          expectedValue: 45,
        },
        {
          values: [createNumericData(500, '0', '200', '300'), createNumericData(30, '0', '20', '10')],
          expectedData: createDataRecord(0, 110, 155),
          expectedValue: 265,
        },
        {
          values: [createNumericData(500, undefined, '200', '300'), createNumericData(30, '', '20', '10')],
          expectedData: createDataRecord(0, 110, 155),
          expectedValue: 265,
        },
        {
          values: [createNumericData(500, undefined, '200', '300'), createNumericData(30, '2', '20', '10')],
          expectedData: createDataRecord(1, 110, 155),
          expectedValue: 265,
        },
      ];

      averageTestCases.forEach(({ values, expectedData, expectedValue }) => {
        it(`should add ${valueType}, expect ${Object.values(expectedData)} and value: ${expectedValue}`, function () {
          const result = new AggregatedUniversalTracker(utr, [], UtrvType.Actual, AggregationMode.Combined);
          values.forEach((v) => {
            result.add(getUtrv(v));
          });
          chai.expect(result.aggregationCount).eqls(values.length);
          chai.expect(result.value).eq(expectedValue);
          chai.expect(result.valueData?.data ?? {}).eqls(expectedData);
        });
      });
    });
  });

  describe('table default (sum) value', function () {

    const utr = JSON.parse(JSON.stringify(utrTableOne));
    [
      {
        utrvOverrides: [
          createUtrvOverrides(new Date('2020-01-01'), [createRow('123', 'oh so great!', 'text col value')]),
          createUtrvOverrides(new Date('2020-01-02'), [createRow('1', '1')]),
        ],
        expectedData: [[
          {
            code: 'col1',
            value: 124
          },
          {
            code: 'col2',
            value: 1
          },
          {
            code: 'col3',
            value: 'text col value'
          }
        ]]
      },
      {
        utrvOverrides: [
          createUtrvOverrides(new Date('2020-01-01'), [createRow('1.23', 12, 'first text')]),
          createUtrvOverrides(new Date('2020-01-03'), [createRow('0', '0', 'third text', 4000, 4001)]),
          createUtrvOverrides(new Date('2020-01-02'), [createRow(0, 3, 'second text', 4000, 4001)]),
        ],
        expectedData: [[
          {
            code: 'col1',
            value: 1.23
          },
          {
            code: 'col2',
            value: 15
          },
          {
            code: 'col3',
            value: 'third text'
          }
        ]]
      },
      {
        utrvOverrides: [
          { effectiveDate: new Date('2020-01-01') },
          { effectiveDate: new Date('2020-01-03') },
          { effectiveDate: new Date('2020-01-02') },
        ],
        expectedData: {},
      },
    ].forEach(({ utrvOverrides, expectedData }) => {

      it(`should add utrv ${utrvOverrides.length} rows, expect ${Object.values(expectedData)}`, function () {
        const result = new AggregatedUniversalTracker(utr);

        utrvOverrides.forEach(utrvOverride => {
          result.add(getUtrvWithUtrId(utr._id, utrvOverride))
        })
        chai.expect(result.aggregationCount).eqls(utrvOverrides.length);
        chai.expect(result.valueData?.table ?? {}).eqls(expectedData);
      });
    })

  });

  describe('table latest value', function () {

    const utr = JSON.parse(JSON.stringify(utrTableOne)) as UniversalTrackerPlain;
    utr.valueValidation?.table?.columns.forEach(c => {
      c.valueAggregation = ColumnValueAggregation.ColumnLatestAggregator
    });

    [
      {
        utrvOverrides: [
          createUtrvOverrides(new Date('2020-01-01'), [createRow('123', 'oh so great!')]),
          createUtrvOverrides(new Date('2020-01-02'), [createRow('1', '1')]),
        ],
        expectedData: [[
          {
            code: 'col1',
            value: '1'
          },
          {
            code: 'col2',
            value: '1'
          }
        ]],
      },
      {
        utrvOverrides: [
          createUtrvOverrides(new Date('2020-01-01'), [createRow('1.23', 12, 'first text')]),
          createUtrvOverrides(new Date('2020-01-03'), [createRow('0', '0', 'third text', 4000, 4001)]),
          createUtrvOverrides(new Date('2020-01-02'), [createRow(0, 3, 'second text', 4000, 4001)]),
        ],
        expectedData: [[
          {
            code: 'col1',
            value: '0'
          },
          {
            code: 'col2',
            value: '0'
          },
          {
            code: 'col3',
            value: 'third text'
          }
        ]]
      },
      {
        utrvOverrides: [
          { effectiveDate: new Date('2020-01-01') },
          { effectiveDate: new Date('2020-01-03') },
          { effectiveDate: new Date('2020-01-02') },
        ],
        expectedData: {},
      },
    ].forEach(({ utrvOverrides, expectedData }) => {

      it(`should add utrv ${utrvOverrides.length} rows, expect ${Object.values(expectedData)}`, function () {
        const result = new AggregatedUniversalTracker(utr);

        utrvOverrides.forEach(utrvOverride => {
          result.add(getUtrvWithUtrId(utr._id, utrvOverride))
        })
        chai.expect(result.aggregationCount).eqls(utrvOverrides.length);
        chai.expect(result.valueData?.table ?? {}).eqls(expectedData);
      });
    })

  });

  describe('table average value', function () {

    const utr = JSON.parse(JSON.stringify(utrTableOne)) as UniversalTrackerPlain;
    utr.valueValidation?.table?.columns.forEach(c => {
      c.valueAggregation = ColumnValueAggregation.ColumnAverageAggregator
    });

    [
      {
        utrvOverrides: [
          createUtrvOverrides(new Date('2020-01-01'), [createRow('123', 'oh so great!', 'text col value')]),
          createUtrvOverrides(new Date('2020-01-02'), [createRow('1', '1')]),
        ],
        expectedData: [[
          {
            code: 'col1',
            value: 62
          },
          {
            code: 'col2',
            value: 0.5,
          },
          {
            code: 'col3',
            value: 'text col value'
          }
        ]]
      },
      {
        utrvOverrides: [
          createUtrvOverrides(new Date('2020-01-01'), [createRow('1.23', 12, 'first text')]),
          createUtrvOverrides(new Date('2020-01-03'), [createRow('0', '0', 'third text', 4000, 4001)]),
          createUtrvOverrides(new Date('2020-01-02'), [createRow(0, 3, 'second text', 4000, 4001)]),
        ],
        expectedData: [[
          {
            code: 'col1',
            value: 0.41
          },
          {
            code: 'col2',
            value: 5,
          },
          {
            code: 'col3',
            value: 'third text'
          }
        ]]
      },
      {
        utrvOverrides: [
          { effectiveDate: new Date('2020-01-01') },
          { effectiveDate: new Date('2020-01-03') },
          { effectiveDate: new Date('2020-01-02') },
        ],
        expectedData: {},
      },
    ].forEach(({ utrvOverrides, expectedData }) => {

      it(`should add utrv ${utrvOverrides.length} rows, expect ${Object.values(expectedData)}`, function () {
        const result = new AggregatedUniversalTracker(utr);

        utrvOverrides.forEach(utrvOverride => {
          result.add(getUtrvWithUtrId(utr._id, utrvOverride))
        })
        chai.expect(result.aggregationCount).eqls(utrvOverrides.length);
        chai.expect(result.valueData?.table ?? {}).eqls(expectedData);
      });
    })

  });

  describe('table with new columnType percentage - average (default) aggregator', function () {

    const utr = JSON.parse(JSON.stringify(utrTableWithColumnTypePercentage)) as UniversalTrackerPlain;

    [
      {
        utrvOverrides: [
          createUtrvOverrides(new Date('2023-01-01'), [createRow(123, 'text value', 20, 80)]),
          createUtrvOverrides(new Date('2023-01-02'), [createRow(1, '1', 40, 10)]),
        ],
        expectedData: [[
          {
            code: 'col1',
            value: 124
          },
          {
            code: 'col2',
            value: '1'
          },
          {
            code: 'col3',
            value: 30
          },
          {
            code: 'col4',
            value: 45
          },
        ]]
      },
      {
        utrvOverrides: [
          createUtrvOverrides(new Date('2023-01-01'), [createRow('1.23', 'first text', -10)]),
          createUtrvOverrides(new Date('2023-01-03'), [createRow('0', 'third text', 0, 0 )]),
          createUtrvOverrides(new Date('2023-01-02'), [createRow(0, 'second text', -50, 0)]),
        ],
        expectedData: [[
          {
            code: 'col1',
            value: 1.23
          },
          {
            code: 'col2',
            value: 'third text'
          },
          {
            code: 'col3',
            value: -20
          },
          {
            code: 'col4',
            value: 0
          }
        ]]
      },
      {
        utrvOverrides: [
          { effectiveDate: new Date('2023-01-01') },
          { effectiveDate: new Date('2023-01-03') },
          { effectiveDate: new Date('2023-01-02') },
        ],
        expectedData: {},
      },
    ].forEach(({ utrvOverrides, expectedData }) => {

      it(`should add utrv ${utrvOverrides.length} rows, expect ${Object.values(expectedData)}`, function () {
        const result = new AggregatedUniversalTracker(utr);

        utrvOverrides.forEach(utrvOverride => {
          result.add(getUtrvWithUtrId(utr._id, utrvOverride))
        })
        chai.expect(result.aggregationCount).eqls(utrvOverrides.length);
        chai.expect(result.valueData?.table ?? {}).eqls(expectedData);
      });
    })

    it(`should add utrv multiple rows from single utrv`, function () {
      // Create a single UTRV with two rows
      const utrvOverrides = [
        createUtrvOverrides(
          new Date(),
          [
            // Row 1: [numeric, text, percentage, percentage]
            createRow(123, 'text value', 20, 80),
            // Row 2: [numeric, text, percentage, percentage]
            createRow(321, 'text value 2', 40, 40),
          ]
        ),
      ];

      // Expected results:
      // - col1 (numeric): sum of values (123 + 321 = 444)
      // - col2 (text): latest value ('text value 2')
      // - col3 (percentage): average of values ((20 + 40) / 2 = 30)
      // - col4 (percentage): average of values ((80 + 40) / 2 = 60)
      const expectedData = [
        [
          { code: 'col1', value: 444 },  // Sum of numeric values
          { code: 'col2', value: 'text value 2' },  // Latest text value
          { code: 'col3', value: 30 },  // Average of percentage values
          { code: 'col4', value: 60 },  // Average of percentage values
        ]
      ];

      const result = new AggregatedUniversalTracker(utr);
      utrvOverrides.forEach(utrvOverride => {
        result.add(getUtrvWithUtrId(utr._id, utrvOverride))
      })

      expect(result.aggregationCount).eqls(utrvOverrides.length);
      expect(result.valueData?.table).eqls(expectedData);
    });

    it(`should add utrv multiple rows, excluding missing values for average`, function () {
      const date = new Date();
      // Create a single UTRV with two rows
      const utrvOverrides = [
        createUtrvOverrides(
          date,
          [
            // Row 1: [numeric, text, percentage, percentage]
            createRow(123, 'text value', 20, 80),
            // Row 2: [numeric, text, percentage, percentage]
            createRow(321, 'text value 2', 40, 40),
          ]
        ),
        createUtrvOverrides(
          date,
          [
            // Row 1: [numeric, text, percentage, percentage]
            createRow(100, 'text value', 60, undefined),
            // Row 2: [numeric, text, percentage, percentage]
            createRow(100, 'text value 2', undefined, undefined),
          ]
        ),
      ];

      const expectedData = [
        [
          { code: 'col1', value: 644 },  // Sum of numeric values (123 + 321 + 100 + 100 = 644)
          { code: 'col2', value: 'text value 2' },  // Latest text value
          { code: 'col3', value: 40 },  // Average of percentage values (20 + 40 + 60) / 3
          { code: 'col4', value: 60 },  // Average of percentage values (80 + 40) / 2
        ]
      ];

      const result = new AggregatedUniversalTracker(utr);
      utrvOverrides.forEach(utrvOverride => {
        result.add(getUtrvWithUtrId(utr._id, utrvOverride))
      })

      expect(result.aggregationCount).eqls(utrvOverrides.length);
      expect(result.valueData?.table).eqls(expectedData);
    });

    it(`should add 3 utrv multiple rows, excluding missing values for average`, function () {
      const date = new Date();
      // Create a single UTRV with two rows
      const utrvOverrides = [
        createUtrvOverrides(
          date,
          [
            // Row 1: [numeric, text, percentage, percentage]
            createRow(123, 'text value', 20, 80),
            // Row 2: [numeric, text, percentage, percentage]
            createRow(321, 'text value 2', 40, 40),
          ]
        ),
        createUtrvOverrides(
          date,
          [
            // Row 1: [numeric, text, percentage, percentage]
            createRow(100, 'text value', 60, undefined),
            // Row 2: [numeric, text, percentage, percentage]
            createRow(100, 'text value 2', undefined, undefined),
          ]
        ),
        createUtrvOverrides(
          date,
          [
            // Row 1: [numeric, text, percentage, percentage]
            createRow(100, 'text value', 60, undefined),
          ]
        ),
      ];

      const expectedData = [
        [
          { code: 'col1', value: 744 },  // Sum of numeric values (123 + 321 + 100 + 100 + 100 = 744)
          { code: 'col2', value: 'text value' },  // Latest text value
          { code: 'col3', value: 45 },  // Average of percentage values (20 + 40 + 60 + 60) / 4 = (180/4) = 45)
          { code: 'col4', value: 60 },  // Average of percentage values (80 + 40) / 2
        ]
      ];

      const result = new AggregatedUniversalTracker(utr);
      utrvOverrides.forEach(utrvOverride => {
        result.add(getUtrvWithUtrId(utr._id, utrvOverride))
      })

      expect(result.aggregationCount).eqls(utrvOverrides.length);
      expect(result.valueData?.table).eqls(expectedData);
    });

  });

  describe('table with new columnType percentage - sum aggregator', function () {

    const utr = JSON.parse(JSON.stringify(utrTableWithColumnTypePercentage)) as UniversalTrackerPlain;
    utr.valueValidation?.table?.columns.forEach(c => {
      c.valueAggregation = ColumnValueAggregation.ColumnSumAggregator
    });

    [
      {
        utrvOverrides: [
          createUtrvOverrides(new Date('2023-01-01'), [createRow(123, 'text value', 20, 80)]),
          createUtrvOverrides(new Date('2023-01-02'), [createRow(1, '1', 40, 10)]),
        ],
        expectedData: [[
          {
            code: 'col1',
            value: 124
          },
          {
            code: 'col2',
            value: '1'
          },
          {
            code: 'col3',
            value: 60
          },
          {
            code: 'col4',
            value: 90
          },
        ]]
      },
      {
        utrvOverrides: [
          createUtrvOverrides(new Date('2023-01-01'), [createRow('1.23', 'first text', -10)]),
          createUtrvOverrides(new Date('2023-01-03'), [createRow('0', 'third text', 0, 0 )]),
          createUtrvOverrides(new Date('2023-01-02'), [createRow(0, 'second text', -50, 120)]),
        ],
        expectedData: [[
          {
            code: 'col1',
            value: 1.23
          },
          {
            code: 'col2',
            value: 'third text'
          },
          {
            code: 'col3',
            value: -60
          },
          {
            code: 'col4',
            value: 120
          }
        ]]
      },
      {
        utrvOverrides: [
          { effectiveDate: new Date('2023-01-01') },
          { effectiveDate: new Date('2023-01-03') },
          { effectiveDate: new Date('2023-01-02') },
        ],
        expectedData: {},
      },
    ].forEach(({ utrvOverrides, expectedData }) => {

      it(`should add utrv ${utrvOverrides.length} rows, expect ${Object.values(expectedData)}`, function () {
        const result = new AggregatedUniversalTracker(utr);

        utrvOverrides.forEach(utrvOverride => {
          result.add(getUtrvWithUtrId(utr._id, utrvOverride))
        })
        chai.expect(result.aggregationCount).eqls(utrvOverrides.length);
        chai.expect(result.valueData?.table ?? {}).eqls(expectedData);
      });
    })

  });

  describe('table with new columnType percentage - latest aggregator', function () {

    const utr = JSON.parse(JSON.stringify(utrTableWithColumnTypePercentage)) as UniversalTrackerPlain;
    utr.valueValidation?.table?.columns.forEach(c => {
      c.valueAggregation = ColumnValueAggregation.ColumnLatestAggregator
    });

    [
      {
        utrvOverrides: [
          createUtrvOverrides(new Date('2023-01-01'), [createRow(123, 'text value', 20, 80)]),
          createUtrvOverrides(new Date('2023-01-02'), [createRow(1, '1', 40, 10)]),
        ],
        expectedData: [[
          {
            code: 'col1',
            value: 1
          },
          {
            code: 'col2',
            value: '1'
          },
          {
            code: 'col3',
            value: 40
          },
          {
            code: 'col4',
            value: 10
          },
        ]]
      },
      {
        utrvOverrides: [
          createUtrvOverrides(new Date('2023-01-01'), [createRow(1.23, 'first text', -10)]),
          createUtrvOverrides(new Date('2023-01-03'), [createRow('0', 'third text', '0')]),
          createUtrvOverrides(new Date('2023-01-02'), [createRow(0, 'second text', -50)]),
        ],
        expectedData: [[
          {
            code: 'col1',
            value: '0'
          },
          {
            code: 'col2',
            value: 'third text'
          },
          {
            code: 'col3',
            value: '0'
          },
        ]]
      },
      {
        utrvOverrides: [
          { effectiveDate: new Date('2023-01-01') },
          { effectiveDate: new Date('2023-01-03') },
          { effectiveDate: new Date('2023-01-02') },
        ],
        expectedData: {},
      },
    ].forEach(({ utrvOverrides, expectedData }) => {

      it(`should add utrv ${utrvOverrides.length} rows, expect ${Object.values(expectedData)}`, function () {
        const result = new AggregatedUniversalTracker(utr);

        utrvOverrides.forEach(utrvOverride => {
          result.add(getUtrvWithUtrId(utr._id, utrvOverride))
        })
        chai.expect(result.aggregationCount).eqls(utrvOverrides.length);
        chai.expect(result.valueData?.table ?? {}).eqls(expectedData);
      });
    })

  });

  describe('table with grouped columns', function () {

    const utr = JSON.parse(JSON.stringify(utrTableOne)) as UniversalTrackerPlain;
    utr.valueAggregation = ValueAggregation.TableRowGroupAggregator;
    utr.valueValidation = {
      ...utr.valueValidation,
      table: {
        columns: utr.valueValidation?.table?.columns ?? [],
        aggregation: {
          type: TableAggregationType.Group,
          columns: [{ code: 'col3' }],
        }
      }
    };

    [
      {
        utrvOverrides: [
          createUtrvOverrides(new Date('2020-01-02'), [
            createRow(1, 10, 'group1'),
            createRow(1, 10, 'group2'),
            createRow(1, 10, 'group3'),
            createRow(1, 10, 'group1'),
            createRow(1, 10, 'group2'),
          ]),
          createUtrvOverrides(new Date('2020-01-02'), [
            createRow(1, 10, 'group1'),
            createRow(1, 10, 'group4'),
            createRow(1, 10, 'group4'),
            createRow(1, 10, 'group5'),
          ]),
        ],
        expectedData: [
          [
            { code: 'col1', value: 3 },
            { code: 'col2', value: 30 },
            { code: 'col3', value: 'group1' }
          ],
          [
            { code: 'col1', value: 2 },
            { code: 'col2', value: 20 },
            { code: 'col3', value: 'group2' }
          ],
          [
            { code: 'col1', value: 1 },
            { code: 'col2', value: 10 },
            { code: 'col3', value: 'group3' }
          ],
          [
            { code: 'col1', value: 2 },
            { code: 'col2', value: 20 },
            { code: 'col3', value: 'group4' }
          ],
          [
            { code: 'col1', value: 1 },
            { code: 'col2', value: 10 },
            { code: 'col3', value: 'group5' }
          ],

        ]
      },
      {
        utrvOverrides: [
          createUtrvOverrides(new Date('2020-01-02'), [
            createRow(2, 20, 'group2'),
            createRow(9, 90, undefined),
          ]),
          createUtrvOverrides(new Date('2020-01-01'), [
            createRow(1, 10, 'group1'),
            createRow(3, 30, 'group3'),
          ]),
        ],
        expectedData: [
          [
            { code: 'col1', value: 2 },
            { code: 'col2', value: 20 },
            { code: 'col3', value: 'group2' }
          ],
          [
            { code: 'col1', value: 9 },
            { code: 'col2', value: 90 },
            { code: 'col3', value: undefined }
          ],
          [
            { code: 'col1', value: 1 },
            { code: 'col2', value: 10 },
            { code: 'col3', value: 'group1' }
          ],
          [
            { code: 'col1', value: 3 },
            { code: 'col2', value: 30 },
            { code: 'col3', value: 'group3' }
          ],
        ]
      },
    ].forEach(({ utrvOverrides, expectedData }) => {
      it(`should group and aggregate rows`, function () {
        const result = new AggregatedUniversalTracker(utr, [], UtrvType.Actual, AggregationMode.Combined);

        utrvOverrides.forEach(utrvOverride => {
          result.add(getUtrvWithUtrId(utr._id, utrvOverride))
        })
        chai.expect(result.aggregationCount).eqls(utrvOverrides.length);
        chai.expect(result.valueData?.table ?? {}).eqls(expectedData);
        chai.expect(result.valueData?.table?.length).eqls(expectedData.length);
      });
    })


    describe('[GU-6147] - Grouped table: TableRowGroupAggregator with percentage column', () => {
      const utr = createUtrFromCode('random-two', {
        valueType: UtrValueType.Table,
        valueAggregation: ValueAggregation.TableRowGroupAggregator,
        valueValidation: {
          table: {
            columns: [
              { code: 'col1', type: ColumnType.Text, name: 'Col1' },
              { code: 'col2', type: ColumnType.Percentage, name: 'Col2' },
              { code: 'col3', type: ColumnType.Number, name: 'Col3' },
            ],
            aggregation: {
              type: TableAggregationType.Group,
              columns: [{ code: 'col1' }],
            }
          },
        },
      });

      const textValue = 'text value';
      const dateOne = '2024-12-31T23:59:59.999Z';

      const dataProvider = [
        {
          name: 'Average for 1 utrvs',
          utrvOverrides: [
            createUtrvOverrides(new Date(dateOne), [
              createRow(textValue, { value: '20.00', numberScale: 'single' }),
              createRow(textValue, '50.00'),
            ]),
          ],
          expectedData: [
            createRow(textValue, 35, undefined), // (20 + 50) / 2
          ],
        },
        {
          name: 'Average for 2 utrvs',
          utrvOverrides: [
            createUtrvOverrides(new Date(dateOne), [
              createRow(textValue, { value: '20.00', numberScale: 'single' }),
              createRow(textValue, '50.00'),
            ]),
            createUtrvOverrides(new Date('2025-12-31T23:59:59.999Z'), [
              createRow(textValue, '80.00'),
            ]),
          ],
          expectedData: [
            createRow(textValue, 50, undefined), // (20 + 50 + 80) / 3
          ],
        },
        {
          name: 'Average for 3 utrvs',
          utrvOverrides: [
            createUtrvOverrides(new Date(dateOne), [
              createRow(textValue, { value: '20.00', numberScale: 'single' }, 10),
              createRow(textValue, '50.00', undefined),
            ]),
            createUtrvOverrides(new Date('2025-12-31T23:59:59.999Z'), [
              createRow(textValue, '80.00'),
            ]),
            createUtrvOverrides(new Date('2026-12-31T23:59:59.999Z'), [
              createRow(textValue, 100.00, 30),
            ]),
          ],
          // (20 + 50 + 80 + 100) / 4 = 250/4 = 62.5, Sum: 10+30=40
          expectedData: [createRow(textValue, 62.5, 40)],
        },
      ];

      dataProvider.forEach(({ name, utrvOverrides, expectedData }, index) => {
        it(`#${index} ${name}`, function () {
          const result = new AggregatedUniversalTracker(utr, [], UtrvType.Actual, AggregationMode.Combined);
          utrvOverrides.forEach(utrvOverride => {
            result.add(getUtrvWithUtrId(utr._id, utrvOverride))
          })
          chai.expect(result.aggregationCount).eqls(utrvOverrides.length);
          chai.expect(result.valueData?.table).deep.eq(expectedData);
        });
      })
    });
    
    describe('Group table with text grouping improvement', () => {
      [
        {
          utrvOverrides: [
            createUtrvOverrides(new Date('2020-01-02'), [
              createRow(1, 10, 'group1'),
              createRow(1, 10, ' group2 '),
              createRow(1, 10, ' group1 '),
              createRow(1, 10, 'group2'),
            ]),
            createUtrvOverrides(new Date('2020-01-02'), [createRow(1, 10, 'Group1 ')]),
          ],
          expectedData: [
            [
              { code: 'col1', value: 3 },
              { code: 'col2', value: 30 },
              { code: 'col3', value: 'Group1' },
            ],
            [
              { code: 'col1', value: 2 },
              { code: 'col2', value: 20 },
              { code: 'col3', value: 'group2' },
            ],
          ],
        },
        {
          utrvOverrides: [
            createUtrvOverrides(new Date('2020-01-02'), [createRow(2, 20, ' group  2 '), createRow(9, 90, undefined)]),
            createUtrvOverrides(new Date('2020-01-01'), [
              createRow(1, 10, ' group  1 '),
              createRow(3, 30, ' group  3 '),
            ]),
          ],
          expectedData: [
            [
              { code: 'col1', value: 2 },
              { code: 'col2', value: 20 },
              { code: 'col3', value: 'group 2' },
            ],
            [
              { code: 'col1', value: 9 },
              { code: 'col2', value: 90 },
              { code: 'col3', value: undefined },
            ],
            [
              { code: 'col1', value: 1 },
              { code: 'col2', value: 10 },
              { code: 'col3', value: 'group 1' },
            ],
            [
              { code: 'col1', value: 3 },
              { code: 'col2', value: 30 },
              { code: 'col3', value: 'group 3' },
            ],
          ],
        },
      ].forEach(({ utrvOverrides, expectedData }) => {
        it(`should ignore space and capitalization when group-by aggregating`, function () {
          const result = new AggregatedUniversalTracker(utr, [], UtrvType.Actual, AggregationMode.Combined);

          utrvOverrides.forEach((utrvOverride) => {
            result.add(getUtrvWithUtrId(utr._id, utrvOverride));
          });
          chai.expect(result.aggregationCount).eqls(utrvOverrides.length);
          chai.expect(result.valueData?.table ?? {}).eqls(expectedData);
        });
      });
    });
  });

  describe('table with empty aggregator column', () => {
    it('should return empty array', () => {
      const utr = createUtr(new ObjectId(), 'table/one', {
        valueType: UtrValueType.Table,
        valueAggregation: ValueAggregation.TableColumnAggregator,
        valueValidation: {
          table: {
            columns: [
              {
                code: 'col_empty',
                name: 'Empty',
                type: ColumnType.Number,
                valueAggregation: ColumnValueAggregation.ColumnEmptyAggregator,
              },
            ],
          },
        },
      });
      const utrvOverrides = [
        createUtrvOverrides(new Date('2020-01-01'), [createRow(1, 10, 'group1')]),
        createUtrvOverrides(new Date('2020-01-02'), [createRow(1, 10, 'group2')]),
      ];
      const result = new AggregatedUniversalTracker(utr, [], UtrvType.Actual, AggregationMode.Combined);
      utrvOverrides.forEach((utrvOverride) => {
        result.add(getUtrvWithUtrId(utr._id, utrvOverride));
      });
      chai.expect(result.aggregationCount).eqls(utrvOverrides.length);
      chai.expect(result.valueData?.table).deep.eq([[]]);
    });
  });

  describe('test NA value', function () {
    const utr = createUtr(new ObjectId(), 'random-two', { valueType: UtrValueType.Number });
    const defaultEffectiveDate = new Date(-1);
    it('should fallback to default effectiveDate', function () {
      const result = new AggregatedUniversalTracker(utr);
      result.add(
        createUtrv(new ObjectId(), utr._id, undefined, {
          valueData: { notApplicableType: 'not_applicable' },
          effectiveDate: undefined,
        })
      );
      chai.expect(result.aggregationCount).eqls(0);
      chai.expect(result.value).eq(undefined);
      chai.expect(result.effectiveDate).deep.eq(defaultEffectiveDate);
    });
    it('should have effectiveDate set instead of falling back to default effectiveDate', function () {
      const result = new AggregatedUniversalTracker(utr);
      const effectiveDate = new Date('2024-01-01');
      result.add(
        createUtrv(new ObjectId(), utr._id, undefined, {
          valueData: { notApplicableType: 'not_applicable' },
          effectiveDate,
        })
      );
      chai.expect(result.aggregationCount).eqls(0);
      chai.expect(result.value).eq(undefined);
      chai.expect(result.effectiveDate).not.deep.eq(defaultEffectiveDate);
      chai.expect(result.effectiveDate).deep.eq(effectiveDate);
    });
  })

});
