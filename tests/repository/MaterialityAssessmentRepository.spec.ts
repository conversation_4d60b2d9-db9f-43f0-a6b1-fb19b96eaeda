import { ObjectId } from 'bson';
import { expect } from 'chai';
import sinon from 'sinon';
import {
  MaterialityAssessmentRepository,
  projectionFields,
} from '../../server/repository/MaterialityAssessmentRepository';
import { excludeSoftDeleted } from '../../server/repository/aggregations';
import { materialityContextUtrCodes } from '../../server/routes/validation-schemas/materiality-assessment';
import { disconnect, connect } from '../setup/mongoInMemory';
import Survey from '../../server/models/survey';
import { clearFixtures, setupSurvey, setupUtr, setupUtrvs } from '../setup/mongoInMemoryFixtures';
import { UtrValueType } from '../../server/models/public/universalTrackerType';
import ContextError from '../../server/error/ContextError';

before(connect);
after(disconnect);

describe('MaterialityAssessmentRepository', () => {
  let surveyModelStub: any;
  let repository: MaterialityAssessmentRepository;

  afterEach(() => {
    sinon.restore();
  });

  describe('getAssessmentData', () => {
    it('should call the aggregate method with the correct pipeline', async () => {
      surveyModelStub = {
        aggregate: sinon.stub().returns({
          exec: sinon.stub().resolves([]),
        }),
      };
      repository = new MaterialityAssessmentRepository(surveyModelStub);

      const surveyId = new ObjectId();
      await repository.getAssessmentData(surveyId);

      const expectedAggregatePipeline = [
        {
          $match: {
            _id: new ObjectId(surveyId),
            ...excludeSoftDeleted(),
          },
        },
        {
          $lookup: {
            from: 'universal-tracker-values',
            localField: 'visibleUtrvs',
            foreignField: '_id',
            as: 'visibleUtrvs',
          },
        },
        {
          $unwind: '$visibleUtrvs',
        },
        {
          $addFields: {
            'visibleUtrvs.latestContributorId': {
              $let: {
                vars: {
                  updatedHistory: {
                    $filter: {
                      input: '$visibleUtrvs.history',
                      cond: { $eq: ['$$this.action', 'updated'] }
                    }
                  }
                },
                in: { $arrayElemAt: ['$$updatedHistory.userId', -1] }
              }
            },
            'visibleUtrvs.latestVerifierId': {
              $cond: {
                if: { $eq: ['$visibleUtrvs.status', 'verified'] },
                then: {
                  $let: {
                    vars: {
                      verifiedHistory: {
                        $filter: {
                          input: '$visibleUtrvs.history',
                          cond: { $in: ['$$this.action', ['verified', 'rejected']] }
                        }
                      }
                    },
                    in: { $arrayElemAt: ['$$verifiedHistory.userId', -1] }
                  }
                },
                else: null
              }
            }
          }
        },
        {
          $lookup: {
            from: 'universal-trackers',
            localField: 'visibleUtrvs.universalTrackerId',
            foreignField: '_id',
            as: 'universalTracker',
          },
        },
        {
          $lookup: {
            from: 'value-list',
            localField: 'universalTracker.valueValidation.valueList.listId',
            foreignField: '_id',
            as: 'valueList',
          },
        },
        {
          $group: {
            _id: '$_id',
            name: { $first: '$name' },
            effectiveDate: { $first: '$effectiveDate' },
            visibleUtrvs: {
              $push: {
                _id: '$visibleUtrvs._id',
                status: '$visibleUtrvs.status',
                valueData: '$visibleUtrvs.valueData',
                universalTracker: {
                  $arrayElemAt: ['$universalTracker', 0],
                },
                valueList: { $arrayElemAt: ['$valueList.options', 0] },
                latestContributorId: '$visibleUtrvs.latestContributorId',
                latestVerifierId: '$visibleUtrvs.latestVerifierId',
              },
            },
          },
        },
        {
          $project: projectionFields,
        },
      ];

      expect(surveyModelStub.aggregate.calledOnce).to.be.true;
      expect(surveyModelStub.aggregate.firstCall.args[0]).to.deep.equal(expectedAggregatePipeline);
    });
  });

  describe('getAssessmentSetupAnswers', () => {
    afterEach(async () => {
      await clearFixtures();
    });

    it('should throw error if no assessment found', async () => {
      repository = new MaterialityAssessmentRepository(Survey);

      await expect(repository.getAssessmentSetupAnswers(new ObjectId())).to.be.eventually.rejectedWith(
        ContextError,
        /Assessment not found/
      );
    });

    it('should return assessment setup answers', async () => {
      const assessmentId = new ObjectId();

      const [operationTimeUtr, numStaffUtr, AnnualSalesUtr, capitalEmployedUtr, sectorUtr] = await Promise.all(
        materialityContextUtrCodes.map(async (utrCode) => {
          return setupUtr({ code: utrCode, valueType: UtrValueType.ValueList });
        })
      );

      const utrvs = await setupUtrvs(
        [operationTimeUtr, numStaffUtr, AnnualSalesUtr, capitalEmployedUtr, sectorUtr].map((utr) => {
          const utrPlain = utr.toObject();
          const valueData = { data: 'yellow', input: { data: 'yellow', unit: undefined, numberScale: undefined } };
          return { utr: utrPlain, utrv: { valueData } };
        })
      );

      await setupSurvey({ _id: assessmentId, name: 'Assessment', visibleUtrvs: utrvs.map(({ _id }) => _id) });

      repository = new MaterialityAssessmentRepository(Survey);
      const result = await repository.getAssessmentSetupAnswers(assessmentId);
      expect(result).to.deep.equal(Object.fromEntries(materialityContextUtrCodes.map((code) => [code, 'yellow'])));
    });
  });
});
